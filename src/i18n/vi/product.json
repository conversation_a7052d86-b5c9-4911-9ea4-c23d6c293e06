{"common": {"groupAndBundleProductIsNotImplemented": "<PERSON>ản phẩm nhóm và gói chưa đư<PERSON><PERSON> cài đặt", "productNotFound": "<PERSON><PERSON><PERSON> ph<PERSON>m không tồn tại", "productVariantNotFound": "<PERSON><PERSON><PERSON><PERSON> thể sản phẩm  không tồn tại", "productAttributeNotFound": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h sản phẩm không tồn tại", "productThumbnailNotFound": "<PERSON><PERSON><PERSON> nhỏ sản phẩm không tồn tại", "externalProductNotFound": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON> bê<PERSON> thứ 3 không hợp lệ", "deleteMultipleError": "<PERSON><PERSON> sách id không hợp lệ", "invalidProductVariantImages": "<PERSON><PERSON> s<PERSON>ch hình <PERSON>nh biến thể của sản phẩm không hợp lệ", "invalidDefaultProductVariant": "<PERSON>i<PERSON><PERSON> thể sản phẩm mặc định không hợp lệ", "invalidCategories": "<PERSON><PERSON> s<PERSON>ch thể loại không hợp lệ", "invalidTags": "<PERSON><PERSON> s<PERSON>ch tag không hợp lệ", "invalidProductVariants": "<PERSON><PERSON> s<PERSON>ch biến thể sản phẩm không hợp lệ", "invalidProductVariantWithSimpleProduct": "<PERSON><PERSON><PERSON> phẩm đơn giản chỉ có thể có 1 biến thể", "invalidProductVariantWithVirtualProduct": "<PERSON><PERSON>n phẩm ảo chỉ có thể có 1 biến thể", "virtualProductMustLinkWithExternalProduct": "<PERSON><PERSON>n phẩm ảo phải có biến thể được liên kết với sản phẩm của bên thứ 3", "onlyVirtualProductCanLinkWithExternalProduct": "Chỉ có sản phẩm ảo có thể có biến thể được liên kết với sản phẩm của bên thứ 3 ", "productTransportInfoNotFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin vận chuyển sản phẩm"}, "validationError": {"id": "Trường id không hợp lệ", "sku": "Trường sku không hợp lệ", "quantity": "Tr<PERSON><PERSON><PERSON> số lượng sản phẩm không hợp lệ", "imageIds": "<PERSON>r<PERSON><PERSON><PERSON> danh sách hình <PERSON>nh không hợp lệ", "productAttributeTermIds": "Tr<PERSON><PERSON><PERSON> danh sách giá trị thuộc t<PERSON>h sản phẩm không hợp lệ", "price": "<PERSON>rư<PERSON><PERSON> gi<PERSON> không hợp lệ", "salePrice": "Trư<PERSON><PERSON> giá gi<PERSON>m không hợp lệ", "point": "<PERSON><PERSON><PERSON><PERSON><PERSON> điểm không hợp lệ", "salePoint": "<PERSON>r<PERSON><PERSON><PERSON> điểm giảm không hợp lệ", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> tên không hợp lệ", "width": "<PERSON>r<PERSON><PERSON><PERSON> chiều rộng không hợp lệ", "height": "Tr<PERSON><PERSON><PERSON> độ cao không hợp lệ", "_length": "<PERSON>r<PERSON><PERSON><PERSON> chiều dài không hợp lệ", "weight": "Tr<PERSON><PERSON><PERSON> cân nặng không hợp lệ", "externalProductId": "Trường id sản phẩm bên thứ 3 không hợp lệ", "lang": "<PERSON><PERSON><PERSON><PERSON><PERSON> ngôn ngữ không hợp lệ", "description": "Tr<PERSON><PERSON><PERSON> mô tả không hợp lệ", "shortDescription": "Tr<PERSON><PERSON><PERSON> mô tả ngắn không hợp lệ", "slug": "Trường slug không hợp lệ", "type": "<PERSON>r<PERSON><PERSON><PERSON> lo<PERSON>i không hợp lệ", "status": "Tr<PERSON><PERSON><PERSON> trạng thái không hợp lệ", "isFeature": "<PERSON>r<PERSON><PERSON><PERSON> sản phẩm nổi bật không hợp lệ", "onSale": "Trư<PERSON><PERSON> đang giảm giá không hợp lệ", "thumbnailId": "Trường mã hình ảnh nhỏ không hợp lệ", "tagIds": "Tr<PERSON><PERSON><PERSON> danh sách thẻ không hợp lệ", "categoryIds": "<PERSON>r<PERSON><PERSON><PERSON> danh sách danh mục không hợp lệ", "productVariantIds": "<PERSON>r<PERSON><PERSON><PERSON> danh sách biến thể sản phẩm không hợp lệ", "defaultProductVariantId": "Trường id biến thể mặc định của sản phẩm không hợp lệ", "productDetails": "Tr<PERSON><PERSON><PERSON> danh sách chi tiết sản phẩm không hợp lệ", "searchType": "Tr<PERSON><PERSON><PERSON> loại tìm kiếm không hợp lệ", "searchText": "<PERSON>r<PERSON><PERSON><PERSON> văn bản tìm kiếm không hợp lệ", "taxStatus": "<PERSON>r<PERSON><PERSON><PERSON> văn bản tìm kiếm không hợp lệ", "productType": "<PERSON>r<PERSON><PERSON><PERSON> lo<PERSON>i sản phẩm không hợp lệ", "productStatus": "Tr<PERSON><PERSON><PERSON> trạng thái sản phẩm không hợp lệ", "sortType": "<PERSON>r<PERSON><PERSON><PERSON> lo<PERSON>i sắp xếp không hợp lệ", "sortField": "<PERSON><PERSON><PERSON><PERSON><PERSON> thu<PERSON><PERSON> t<PERSON>h sắp xếp không hợp lệ", "attributeTermIds": "Tr<PERSON><PERSON><PERSON> danh sách id giá trị thuộc tính không hợp lệ", "minPoint": "Tr<PERSON><PERSON><PERSON> điểm tối đa không hợp lệ", "maxPoint": "Tr<PERSON><PERSON><PERSON> điếm tối thiểu không hợp lệ", "numberOfProductPerCategory": "Trường số lượng sản phẩm với mỗi danh mục không hợp lệ"}, "productCannotBeDeleted": "<PERSON><PERSON><PERSON> ph<PERSON>m không thể bị xoá", "productVariantCannotBeDeleted": "<PERSON><PERSON><PERSON><PERSON> thể sản phẩm không thể bị xoá", "productAttributeCannotBeDeleted": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h sản phẩm không thể bị xoá", "productAttributeTermCannotBeDeleted": "<PERSON><PERSON><PERSON> trị thuộc t<PERSON>h sản phẩm không thể bị xoá", "cannotDeleteProductVariantOfProduct": "<PERSON><PERSON><PERSON><PERSON> thể xoá biến thể sản phẩm, chỉ có thể thêm"}