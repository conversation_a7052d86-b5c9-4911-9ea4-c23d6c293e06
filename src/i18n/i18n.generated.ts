/* DO NOT EDIT, file generated by nestjs-i18n */

/* eslint-disable */
/* prettier-ignore */
import { Path } from "nestjs-i18n";
/* prettier-ignore */
export type I18nTranslations = {
    "add-point-code": {
        "overNumberCodeInMonth": string;
        "fileNotDownloaded": string;
        "systemConflict": string;
        "unBLockScanCodeDupConflict": string;
    };
    "auth": {
        "common": {
            "deleteMultipleError": string;
            "wrongOldPassword": string;
            "invalidEmail": string;
            "invalidUser": string;
            "invalidToken": string;
            "expiredToken": string;
            "wrongPassword": string;
            "invalidPassword": string;
            "featureIsTurnOff": string;
        };
        "customer": {
            "passwordRequired": string;
            "wrongPhoneNumber": string;
            "wrongPassword": string;
            "failPhoneNumber": string;
            "failPassword": string;
            "phoneNumberNotFound": string;
            "customerNotFound": string;
            "blockedAccount": string;
            "unBlockAccount": string;
            "blockedScanQrCode": string;
            "unBlockedScanQrCode": string;
            "existed": string;
            "zaloLoginFailed": string;
            "wrongOldPassword": string;
        };
        "merchant": {
            "invalidEmail": string;
            "invalidPassword": string;
            "invalidCredentials": string;
            "invalidName": string;
            "invalidAddress": string;
            "invalidPhoneNumber": string;
            "unverifiedMerchant": string;
            "outDateUserToken": string;
            "wrongUserToken": string;
            "merchantNotFound": string;
            "existed": string;
        };
        "admin": {
            "invalidEmail": string;
            "wrongPassword": string;
            "failUserName": string;
            "adminNotFound": string;
        };
        "userGroup": {
            "userGroupNotFound": string;
            "deleteMultipleError": string;
        };
        "agent": {
            "agentNotFound": string;
        };
        "mustUpdateAllProvinceDistrictWard": string;
        "provinceNotFound": string;
        "districtNotFound": string;
        "wardNotFound": string;
        "districtNotMatchParent": string;
        "wardNotMatchParent": string;
        "provinceTypeIsNotProvince": string;
        "provinceTypeIsNotDistrict": string;
        "provinceTypeIsNotWard": string;
        "customerShippingNotFound": string;
        "cannotDeleteLastCustomerShipping": string;
        "offForgetPassword": string;
        "onThresholdSendOtp": string;
        "blockedLogin": string;
    };
    "cart": {
        "invalidProduct": string;
        "productVariantIsNotBelongToOneProduct": string;
        "productOutOfStock": string;
        "invalidProductToVariantIds": string;
        "productToVariantsNotBelongsToOneProduct": string;
    };
    "category": {
        "cannotBeDeleted": string;
    };
    "common": {
        "word": {
            "name": string;
            "notFound": string;
            "birthDate": string;
            "firebaseIdToken": string;
            "referralCode": string;
            "invalid": string;
            "password": string;
            "addPointCode": string;
            "downloadFileHistory": string;
            "fileRequest": string;
            "fileRequestQrToGift": string;
            "fileRequestQrToPoint": string;
            "qrCode": string;
            "fileRequestDownloadHistory": string;
            "systemConfigPoint": string;
            "admin": string;
            "customer": string;
            "merchant": string;
            "userGroup": string;
            "userGroupToUser": string;
            "customerToken": string;
            "user": string;
            "cartLineItem": string;
            "cart": string;
            "groupPolicy": string;
            "groupToPolicy": string;
            "policy": string;
            "userToGroupPolicy": string;
            "categoryDetail": string;
            "category": string;
            "cronJobExecutionInfo": string;
            "cronJob": string;
            "eventHistory": string;
            "eventReward": string;
            "eventSku": string;
            "event": string;
            "requestExport": string;
            "expressDeliveryHistory": string;
            "file": string;
            "gameCode": string;
            "gameGift": string;
            "gameUserInfo": string;
            "gameWinHistory": string;
            "game": string;
            "requestImport": string;
            "notificationToUser": string;
            "notification": string;
            "userNotiSetting": string;
            "externalProductUsedInfo": string;
            "orderBilling": string;
            "orderLineItem": string;
            "orderShipping": string;
            "order": string;
            "orderEVoucher": string;
            "blockedHistory": string;
            "scanHistory": string;
            "userHistoryPoint": string;
            "userNumberScan": string;
            "userPointMat": string;
            "userPoint": string;
            "externalProductProvider": string;
            "externalProduct": string;
            "productAttributeDetail": string;
            "productAttributeTermDetail": string;
            "productAttributeTerm": string;
            "productAttribute": string;
            "productCategory": string;
            "productDetail": string;
            "productToAttribute": string;
            "productToTag": string;
            "productToVariant": string;
            "productTransportInfo": string;
            "productVariantDetail": string;
            "productVariantPoint": string;
            "productVariantImage": string;
            "productVariant": string;
            "product": string;
            "province": string;
            "externalReferralHistory": string;
            "externalReferrer": string;
            "referralHistory": string;
            "surveyAnswer": string;
            "surveyQuestion": string;
            "survey": string;
            "userSurvetAnswer": string;
            "userSurveyHistory": string;
            "secret": string;
            "systemConfig": string;
            "tag": string;
            "termPolicyDetail": string;
            "termsPolicy": string;
            "tierConfig": string;
            "phoneNumber": string;
            "deviceToken": string;
            "email": string;
            "address": string;
            "imageId": string;
            "newPassword": string;
            "gameType": string;
            "gameGiftConstraint": string;
            "gameGiftProvinceConstraint": string;
            "gameGiftAllocationConstraint": string;
            "gamePlayHistory": string;
            "appConfig": string;
            "customerShipping": string;
            "gamePlayTimeConfig": string;
            "gamePlayTime": string;
            "orderRefund": string;
            "store": string;
            "provinceKey": string;
            "subject": string;
            "subjectDetail": string;
            "news": string;
            "newsDetail": string;
            "feedback": string;
            "newsToSubject": string;
            "newsToFile": string;
            "gameGiftProvinceQuantity": string;
            "externalToken": string;
            "outboxMessage": string;
            "inboxMessage": string;
            "notiToUserGroup": string;
            "sfNotiCustomer": string;
            "sfOrderLine": string;
            "sfTranasction": string;
            "irisTransactionLog": string;
            "notiToUser": string;
            "noti": string;
        };
        "exc": {
            "badRequest": string;
            "notFound": string;
            "internalServerError": string;
            "forbidden": string;
            "unauthorized": string;
            "conflict": string;
            "expectationFailed": string;
            "serviceUnavailable": string;
        };
        "validationError": {
            "merchantId": string;
            "date": string;
        };
    };
    "event": {
        "invalidEventGift": string;
        "deleteMultipleError": string;
        "notFound": string;
        "invalidSystemConfigPoint": string;
    };
    "feedback": {
        "existsFeedbackContent": string;
    };
    "game": {
        "productVariantNotFound": string;
        "productVariantNotLinkWithVoucher": string;
        "invalidProvince": string;
        "gameNotFound": string;
        "gameGiftNotFound": string;
        "cannotDeleteGameAlreadyHasWinner": string;
        "cannotDeleteGameGiftAlreadyHasWinner": string;
        "conflictGameType": string;
        "invalidGameTypeIds": string;
        "gameTypeNotFound": string;
        "cannotDeleteGameTypeHasBeenUsed": string;
        "invalidGameGiftConstraints": string;
        "invalidGameGiftProvinceConstraints": string;
        "invalidGameGiftAllocationConstraints": string;
        "invalidGameIds": string;
        "gameConfigPlayTimeNotFound": string;
        "invalidGameConfigPlayTimeIds": string;
        "notEnoughGamePlayTime": string;
        "userNotSelectProvince": string;
        "gameInvalid": string;
        "gameGiftConstraintTypeInvalid": string;
        "invalidProduct": string;
        "invalidProductVariant": string;
        "gameNotHaveNotWonGift": string;
        "winRateMoreThan100": string;
    };
    "iris": {
        "pinCode": {
            "getSoftPinError": string;
            "checkSoftPinError": string;
            "getTransactionResultError": string;
        };
        "topup": {
            "error": string;
            "getTopupResultError": string;
        };
    };
    "news": {
        "isExisted": string;
    };
    "noti": {
        "common": {
            "notiNotFound": string;
            "deleteMultipleError": string;
            "featureIsTurnOff": string;
        };
        "canNotDeleteNotiSent": string;
        "canNotDeleteSaleForceNoti": string;
    };
    "order-e-voucher": {
        "eVoucherIsUsed": string;
        "eVoucherIsExpired": string;
        "eVoucherIsBlocked": string;
        "eVoucherIsFailed": string;
        "eVoucherIsFailedManyTimes": string;
        "eVoucherNotFound": string;
        "eVoucherIsNotValid": string;
        "eVoucherIsNotValidTelecom": string;
        "topup": {
            "error": string;
            "transactionFailed": string;
            "transactionTimeout": string;
        };
        "pinCode": {
            "error": string;
            "transactionFailed": string;
            "transactionTimeout": string;
        };
    };
    "order": {
        "addOrderShipping": {
            "overOrderShipping": string;
            "addressIsOnlyOne": string;
            "orderShippingNotFound": string;
        };
        "featureIsTurnOff": string;
        "cartNotFound": string;
        "productNotFound": string;
        "invalidProduct": string;
        "productOutOfStock": string;
        "productExpired": string;
        "missingOrderShippingInfo": string;
        "notEnoughPoint": string;
        "paymentPriceUnimplemented": string;
        "missingPhoneNumber": string;
        "voucherUsed": string;
        "cannotRefundOrder": string;
        "invalidRefundPoint": string;
        "provinceNotFound": string;
        "districtNotFound": string;
        "wardNotFound": string;
        "districtNotMatchParent": string;
        "wardNotMatchParent": string;
        "provinceTypeIsNotProvince": string;
        "provinceTypeIsNotDistrict": string;
        "provinceTypeIsNotWard": string;
        "customerShippingNotFound": string;
        "cannotDeleteLastCustomerShipping": string;
        "requestedRefundOrder": string;
        "refundedOrder": string;
        "cannotRejectRefundOrder": string;
        "refundVoucherUnimplemented": string;
    };
    "outbox-message": {
        "outboxMessageNotFailed": string;
    };
    "point": {
        "addPoint": {
            "invalidCode": string;
            "usedCode": string;
            "expiredCode": string;
            "blockedUser": string;
            "blockedScanQrCode": string;
            "overAddPointInDay": string;
        };
        "notEnoughPoint": string;
        "featureIsTurnOff": string;
    };
    "product": {
        "common": {
            "groupAndBundleProductIsNotImplemented": string;
            "productNotFound": string;
            "productVariantNotFound": string;
            "productAttributeNotFound": string;
            "productThumbnailNotFound": string;
            "externalProductNotFound": string;
            "deleteMultipleError": string;
            "invalidProductVariantImages": string;
            "invalidDefaultProductVariant": string;
            "invalidCategories": string;
            "invalidTags": string;
            "invalidProductVariants": string;
            "invalidProductVariantWithSimpleProduct": string;
            "invalidProductVariantWithVirtualProduct": string;
            "virtualProductMustLinkWithExternalProduct": string;
            "onlyVirtualProductCanLinkWithExternalProduct": string;
            "productTransportInfoNotFound": string;
        };
        "validationError": {
            "id": string;
            "sku": string;
            "quantity": string;
            "imageIds": string;
            "productAttributeTermIds": string;
            "price": string;
            "salePrice": string;
            "point": string;
            "salePoint": string;
            "name": string;
            "width": string;
            "height": string;
            "_length": string;
            "weight": string;
            "externalProductId": string;
            "lang": string;
            "description": string;
            "shortDescription": string;
            "slug": string;
            "type": string;
            "status": string;
            "isFeature": string;
            "onSale": string;
            "thumbnailId": string;
            "tagIds": string;
            "categoryIds": string;
            "productVariantIds": string;
            "defaultProductVariantId": string;
            "productDetails": string;
            "searchType": string;
            "searchText": string;
            "taxStatus": string;
            "productType": string;
            "productStatus": string;
            "sortType": string;
            "sortField": string;
            "attributeTermIds": string;
            "minPoint": string;
            "maxPoint": string;
            "numberOfProductPerCategory": string;
        };
        "productCannotBeDeleted": string;
        "productVariantCannotBeDeleted": string;
        "productAttributeCannotBeDeleted": string;
        "productAttributeTermCannotBeDeleted": string;
        "cannotDeleteProductVariantOfProduct": string;
    };
    "qr-code": {
        "overNumberCodeInMonth": string;
        "fileNotDownloaded": string;
        "systemConflict": string;
        "unBLockScanCodeDupConflict": string;
        "scan": {
            "invalidCode": string;
            "usedCode": string;
            "expiredCode": string;
            "blockedScan": string;
            "overScanSuccessInDay": string;
            "overScanFailedInDay": string;
            "blockScanQrCode": string;
            "blockAccount": string;
        };
    };
    "referral": {
        "referrerNotFound": string;
        "referrerExisted": string;
        "invalidFrom": string;
        "invalidTo": string;
        "invalidName": string;
        "invalidPhoneNumber": string;
        "invalidCode": string;
        "invalidAddress": string;
        "invalidStatus": string;
        "invalidReferralCode": string;
        "invalidId": string;
        "fileTypeInvalid": string;
        "referralYourSelf": string;
        "conflictCodeOrReferral": string;
        "featureIsTurnOff": string;
        "referrerHadInvitedUser": string;
    };
    "store": {
        "storeNotFound": string;
        "fileTypeInvalid": string;
    };
    "subject": {
        "isExisted": string;
    };
    "survey": {
        "notFound": string;
        "simpleQuestionOnlyAcceptOneAnswer": string;
        "notAnswerEnoughQuestion": string;
        "surveyIsAlreadyTaken": string;
        "featureIsTurnOff": string;
        "answerNotFound": string;
        "canNotUpdateSurveyPointIfAlreadyTaken": string;
    };
    "system-config": {
        "common": {
            "systemConfigNotFound": string;
            "invalidTitle": string;
            "invalidStatus": string;
            "invalidLink": string;
            "invalidDesc": string;
            "invalidImage": string;
            "invalidImageId": string;
            "invalidId": string;
            "invalidStartDate": string;
            "invalidEndDate": string;
            "invalidName": string;
            "invalidMaxLength": string;
            "invalidOrdinal": string;
        };
        "popupConfig": {
            "popupConfigNotFound": string;
            "popupConfigItemNotFound": string;
            "popupConfigOrdinalAlready": string;
        };
        "featureConfig": {
            "featureConfigNotFound": string;
            "featureConfigItemNotFound": string;
            "popupConfigItemNotFound": string;
        };
        "ruleConfig": {
            "ruleConfigNotFound": string;
            "ruleConfigItemNotFound": string;
        };
        "eventConfig": {
            "eventConfigNotFound": string;
            "eventConfigItemNotFound": string;
        };
        "homeConfig": {
            "common": {
                "invalidData": string;
                "homeConfigNotFound": string;
            };
        };
        "shareAppConfig": {
            "common": {
                "invalidData": string;
                "shareAppConfigNotFound": string;
            };
        };
    };
    "tag": {
        "cannotBeDeleted": string;
    };
    "terms-policy": {
        "common": {
            "termsPolicyNotFound": string;
            "termsPolicyVariantNotFound": string;
            "termsPolicyAttributeNotFound": string;
            "termsPolicyThumbnailNotFound": string;
            "externalTermsPolicyNotFound": string;
            "deleteMultipleError": string;
        };
        "validationError": {
            "id": string;
            "name": string;
            "lang": string;
            "content": string;
            "type": string;
            "status": string;
            "termsPolicyDetails": string;
        };
    };
    "test": {
        "plain": string;
        "params": string;
    };
    "token": {
        "otpLimitReach": string;
        "invalidOtpType": string;
        "otpExpires": string;
        "incorrectOtp": string;
    };
};
/* prettier-ignore */
export type I18nPath = Path<I18nTranslations>;
