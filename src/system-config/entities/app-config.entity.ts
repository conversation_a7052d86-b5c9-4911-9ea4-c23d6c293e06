import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { User } from '../../auth/entities/user.entity';
import { DecimalColumn } from '../../common/decorators/typeorm.decorator';
import { BaseEntity } from '../../common/entities/base.entity';
import { DeviceType } from '../enums/app-config.enum';

@Entity({ name: 'app_config' })
export class AppConfig extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @DecimalColumn({ scale: 1, precision: 12 })
  mobileVersion: number;

  @Column({ enum: DeviceType, type: 'enum' })
  deviceType: DeviceType;

  @Column({ nullable: false, name: 'owner_id' })
  ownerId: number;

  @ManyToOne(() => User, (user) => user.appConfigs)
  @JoinColumn()
  onwer: User;
}
