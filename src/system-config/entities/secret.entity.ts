import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>o<PERSON><PERSON>,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { User } from '../../auth/entities/user.entity';
import { PartialIndexWithSoftDelete } from '../../common/decorators/typeorm.decorator';
import { BaseEntity } from '../../common/entities/base.entity';
import { SecretKey } from '../enums/secret.enum';

@Entity()
@PartialIndexWithSoftDelete(['key', 'ownerId'], { unique: true })
export class Secret extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ enum: SecretKey, type: 'enum' })
  key: SecretKey;

  @Column({ type: 'bytea' })
  value: any;

  // Join user
  @Column({ name: 'owner_id' })
  ownerId: number;

  @ManyToOne(() => User, (u) => u.secrets)
  @JoinColumn({ name: 'owner_id' })
  owner: User;
  // End join user
}
