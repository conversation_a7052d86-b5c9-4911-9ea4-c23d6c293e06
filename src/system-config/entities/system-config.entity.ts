import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON><PERSON>,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { User } from '../../auth/entities/user.entity';
import { PartialIndexWithSoftDelete } from '../../common/decorators/typeorm.decorator';
import { BaseEntity } from '../../common/entities/base.entity';
import { SystemConfigKey } from '../enums/index.num';

@Entity()
@PartialIndexWithSoftDelete(['ownerId', 'key'], { unique: true })
export class SystemConfig extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 255 })
  key: SystemConfigKey;

  @Column({ type: 'jsonb' })
  value: any;

  // Join user
  @Column({ name: 'owner_id' })
  ownerId: number;

  @ManyToOne(() => User, (user) => user.systemConfigs)
  @JoinColumn({ name: 'owner_id' })
  owner: User;
  // End join user
}
