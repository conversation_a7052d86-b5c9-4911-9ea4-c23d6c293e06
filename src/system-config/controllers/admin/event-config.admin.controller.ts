import { Controller, Get, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { PrefixType } from '../../../common/constants/global.constant';
import { AuthenticateAdmin } from '../../../common/decorators/auth.decorator';
import { GetListFeatureConfigAdminReqDto } from '../../dtos/admin/req/feature-config.admin.req.dto';
import { EventConfigAdminService } from '../../services/admin/event-config.admin.service';

@Controller(`${PrefixType.ADMIN}/event-config`)
@ApiTags('Event Config Admin')
@AuthenticateAdmin()
export class EventConfigAdminController {
  constructor(private eventConfigService: EventConfigAdminService) {}

  @Get()
  get(@Query() body: GetListFeatureConfigAdminReqDto) {
    return this.eventConfigService.get(body);
  }
}
