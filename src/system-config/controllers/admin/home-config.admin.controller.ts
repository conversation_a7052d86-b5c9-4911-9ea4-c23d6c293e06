import { Controller, Get, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { PrefixType } from '../../../common/constants/global.constant';
import { AuthenticateAdmin } from '../../../common/decorators/auth.decorator';
import { GetListHomeConfigAdminReqDto } from '../../dtos/admin/req/home-config.admin.req.dto';
import { HomeConfigAdminService } from '../../services/admin/home-config.admin.service';

@Controller(`${PrefixType.ADMIN}/home-config`)
@ApiTags('Home Config Admin')
@AuthenticateAdmin()
export class HomeConfigAdminController {
  constructor(private homeConfigService: HomeConfigAdminService) {}

  @Get()
  get(@Query() body: GetListHomeConfigAdminReqDto) {
    return this.homeConfigService.get(body);
  }
}
