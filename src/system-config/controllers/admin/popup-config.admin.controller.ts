import { Controller, Get, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { PrefixType } from '../../../common/constants/global.constant';
import { AuthenticateAdmin } from '../../../common/decorators/auth.decorator';
import { GetListPopupConfigAdminReqDto } from '../../dtos/admin/req/popup-config.admin.dto';
import { PopupConfigAdminService } from '../../services/admin/popup-config.merchant.admin.service';

@Controller(`${PrefixType.ADMIN}/popup-config`)
@ApiTags('Popup Config Admin')
@AuthenticateAdmin()
export class PopupConfigAdminController {
  constructor(private popupConfigService: PopupConfigAdminService) {}

  @Get()
  get(@Query() body: GetListPopupConfigAdminReqDto) {
    return this.popupConfigService.get(body);
  }
}
