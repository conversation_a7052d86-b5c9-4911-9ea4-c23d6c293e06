import { Controller, Get, Post, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { User } from '../../../auth/entities/user.entity';
import { PrefixType } from '../../../common/constants/global.constant';
import {
  AuthenticateAdmin,
  CurrentAuthData,
} from '../../../common/decorators/auth.decorator';
import {
  GetListFeatureConfigAdminReqDto,
  PostConfigAdminReqDto,
} from '../../dtos/admin/req/feature-config.admin.req.dto';
import { FeatureConfigAdminService } from '../../services/admin/feature-config.admin.service';

@Controller(`${PrefixType.ADMIN}/feature-config`)
@ApiTags('Feature Config Admin')
@AuthenticateAdmin()
export class FeatureConfigAdminController {
  constructor(private featureConfigService: FeatureConfigAdminService) {}

  @Get()
  get(@Query() body: GetListFeatureConfigAdminReqDto) {
    return this.featureConfigService.get(body);
  }

  @Post()
  createConfig(
    @Query() body: PostConfigAdminReqDto,
    @CurrentAuthData() user: User,
  ) {
    return this.featureConfigService.createConfig(body, user);
  }
}
