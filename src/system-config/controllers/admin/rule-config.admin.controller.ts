import { Controller, Get, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { PrefixType } from '../../../common/constants/global.constant';
import { AuthenticateAdmin } from '../../../common/decorators/auth.decorator';
import { GetListRuleConfigAdminReqDto } from '../../dtos/admin/req/rule-config.admin.req.dto';
import { RuleConfigAdminService } from '../../services/admin/rule-config.admin.service';

@Controller(`${PrefixType.ADMIN}/rule-config`)
@ApiTags('Rule Config Admin')
@AuthenticateAdmin()
export class RuleConfigAdminController {
  constructor(private ruleConfigAdminService: RuleConfigAdminService) {}

  @Get()
  get(@Query() body: GetListRuleConfigAdminReqDto) {
    return this.ruleConfigAdminService.get(body);
  }
}
