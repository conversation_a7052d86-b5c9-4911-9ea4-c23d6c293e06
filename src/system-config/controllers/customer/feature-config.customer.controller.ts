import { Controller, Get, Param } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { PrefixType } from '../../../common/constants/global.constant';
import {
  MerchantId,
  MerchantIdHeader,
} from '../../../common/decorators/auth.decorator';
import { FeatureConfigCustomerService } from '../../services/customer/feature-config.customer.service';

@Controller(`${PrefixType.CUSTOMER}/feature-config`)
@ApiTags('Feature Config Customer')
export class FeatureConfigCustomerController {
  constructor(private featureConfigService: FeatureConfigCustomerService) {}

  @Get()
  @MerchantIdHeader()
  get(@MerchantId() merchantId: number) {
    return this.featureConfigService.get(merchantId);
  }

  @Get(':key')
  @MerchantIdHeader()
  getByKey(@MerchantId() merchantId: number, @Param('key') key: string) {
    return this.featureConfigService.getByKey(Number(merchantId), key);
  }
}
