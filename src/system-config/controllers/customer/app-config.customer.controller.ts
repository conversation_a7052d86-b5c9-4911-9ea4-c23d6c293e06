import { Controller, Get, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { PrefixType } from '../../../common/constants/global.constant';
import {
  MerchantId,
  MerchantIdHeader,
} from '../../../common/decorators/auth.decorator';
import { GetMobileAppVersionCustomerReqDto } from '../../dtos/customer/app-config.customer.req.dto';
import { AppConfigCustomerService } from '../../services/customer/app-config.customer.service';

@Controller(`${PrefixType.CUSTOMER}/app-config`)
@ApiTags('App Config Customer')
export class AppConfigCustomerController {
  constructor(private appConfigCustomerService: AppConfigCustomerService) {}

  @Get()
  @MerchantIdHeader()
  get(
    @MerchantId() merchantId: number,
    @Query() query: GetMobileAppVersionCustomerReqDto,
  ) {
    return this.appConfigCustomerService.getMobileAppVersion(merchantId, query);
  }
}
