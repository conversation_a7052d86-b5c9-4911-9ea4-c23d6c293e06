import { Controller, Get, Param } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { PrefixType } from '../../../common/constants/global.constant';
import {
  MerchantId,
  MerchantIdHeader,
} from '../../../common/decorators/auth.decorator';
import { EventConfigCustomerService } from '../../services/customer/event-config.customer.service';

@Controller(`${PrefixType.CUSTOMER}/event-config`)
@ApiTags('Event Config Customer')
export class EventConfigCustomerController {
  constructor(private eventConfigCustomerService: EventConfigCustomerService) {}

  @Get()
  @MerchantIdHeader()
  get(@MerchantId() merchantId: number) {
    return this.eventConfigCustomerService.get(merchantId);
  }

  @Get(':key')
  @MerchantIdHeader()
  getByKey(@MerchantId() merchantId: number, @Param('key') key: string) {
    return this.eventConfigCustomerService.getByKey(Number(merchantId), key);
  }
}
