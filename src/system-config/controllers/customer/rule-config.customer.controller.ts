import { Controller, Get, Param } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { PrefixType } from '../../../common/constants/global.constant';
import {
  MerchantId,
  MerchantIdHeader,
} from '../../../common/decorators/auth.decorator';
import { RuleConfigCustomerService } from '../../services/customer/rule-config.customer.service';

@Controller(`${PrefixType.CUSTOMER}/rule-config`)
@ApiTags('Rule Config Customer')
export class RuleConfigCustomerController {
  constructor(private ruleConfigService: RuleConfigCustomerService) {}

  @Get()
  @MerchantIdHeader()
  get(@MerchantId() merchantId: number) {
    return this.ruleConfigService.get(merchantId);
  }

  @Get(':key')
  @MerchantIdHeader()
  getByKey(@MerchantId() MerchantId: number, @Param('key') key: string) {
    return this.ruleConfigService.getByKey(Number(MerchantId), key);
  }
}
