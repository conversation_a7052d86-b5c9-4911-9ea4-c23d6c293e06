import { Controller, Get } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { UseCache } from '../../../cache/decorators/cache.decorator';
import { <PERSON><PERSON><PERSON><PERSON> } from '../../../cache/enums/cache.enum';
import { PrefixType } from '../../../common/constants/global.constant';
import {
  MerchantId,
  MerchantIdHeader,
} from '../../../common/decorators/auth.decorator';
import { ShareAppConfigResDto } from '../../dtos/common/res/share-app-config.res.dto';
import { ShareAppConfigCustomerService } from '../../services/customer/share-app-config.customer.service';

@Controller(`${PrefixType.CUSTOMER}/share-app-config`)
@ApiTags('Share App Config Customer')
export class ShareAppConfigCustomerController {
  constructor(private shareAppConfigService: ShareAppConfigCustomerService) {}

  @Get()
  @MerchantIdHeader()
  @UseCache(CacheKey.SHARE_APP_CONFIG, ShareAppConfigResDto)
  get(@MerchantId() merchantId: number) {
    return this.shareAppConfigService.get(merchantId);
  }
}
