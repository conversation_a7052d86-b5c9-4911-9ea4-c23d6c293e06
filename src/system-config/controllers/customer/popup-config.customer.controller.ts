import { Controller, Get } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { PrefixType } from '../../../common/constants/global.constant';
import {
  MerchantId,
  MerchantIdHeader,
} from '../../../common/decorators/auth.decorator';
import { PopupConfigCustomerService } from '../../services/customer/popup-config.customer.service';

@Controller(`${PrefixType.CUSTOMER}/popup-config`)
@ApiTags('Popup Config Customer')
export class PopupConfigCustomerController {
  constructor(private popupConfigService: PopupConfigCustomerService) {}

  @Get('/available')
  @MerchantIdHeader()
  getAvailable(@MerchantId() merchantId: number) {
    return this.popupConfigService.getAvailable(merchantId);
  }
}
