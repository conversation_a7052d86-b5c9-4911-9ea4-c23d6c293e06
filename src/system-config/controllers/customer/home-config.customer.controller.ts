import { Controller, Get } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { PrefixType } from '../../../common/constants/global.constant';
import {
  MerchantId,
  MerchantIdHeader,
} from '../../../common/decorators/auth.decorator';
import { HomeConfigCustomerService } from '../../services/customer/home-config.customer.service';

@Controller(`${PrefixType.CUSTOMER}/home-config`)
@ApiTags('Home Config Customer')
export class HomeConfigCustomerController {
  constructor(private homeConfigService: HomeConfigCustomerService) {}

  @Get()
  @MerchantIdHeader()
  // @UseCache(CacheKey.HOME_CONFIG, HomeConfigResDto)
  get(@MerchantId() merchantId: number) {
    return this.homeConfigService.get(merchantId);
  }
}
