import { Body, Controller, Get, Put } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { User } from '../../../auth/entities/user.entity';
import { PrefixType } from '../../../common/constants/global.constant';
import {
  AuthorizeMerchant,
  CurrentAuthData,
} from '../../../common/decorators/auth.decorator';
import { Action, Resource } from '../../../common/enums/casl.enum';
import { UpdateFeatureConfigMerchantReqDto } from '../../dtos/merchant/req/feature-config.merchant.req.dto';
import { FeatureConfigMerchantService } from '../../services/merchant/feature-config.merchant.service';

@Controller(`${PrefixType.MERCHANT}/feature-config`)
@ApiTags('Feature Config Merchant')
@AuthorizeMerchant({ action: Action.READ, resource: Resource.SYSTEM_CONFIG })
export class FeatureConfigMerchantController {
  constructor(private featureConfigService: FeatureConfigMerchantService) {}

  @Get()
  get(@CurrentAuthData() user: User) {
    return this.featureConfigService.get(user);
  }

  @Put()
  @AuthorizeMerchant({
    action: Action.UPDATE,
    resource: Resource.SYSTEM_CONFIG,
  })
  update(
    @Body() body: UpdateFeatureConfigMerchantReqDto,
    @CurrentAuthData() user: User,
  ) {
    return this.featureConfigService.update(body, user);
  }
}
