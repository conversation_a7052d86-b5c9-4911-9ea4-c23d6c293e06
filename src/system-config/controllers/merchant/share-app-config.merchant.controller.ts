import { Body, Controller, Get, Post, Put } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { User } from '../../../auth/entities/user.entity';
import { PrefixType } from '../../../common/constants/global.constant';
import {
  AuthorizeMerchant,
  CurrentAuthData,
} from '../../../common/decorators/auth.decorator';
import { Action, Resource } from '../../../common/enums/casl.enum';
import { UpdateShareAppConfigMerchantReqDto } from '../../dtos/merchant/req/share-app-config.merchant.req.dto';
import { ShareAppConfigMerchantService } from '../../services/merchant/share-app-config.merchant.service';

@Controller(`${PrefixType.MERCHANT}/share-app-config`)
@ApiTags('Share App Config Merchant')
@AuthorizeMerchant({ action: Action.READ, resource: Resource.SYSTEM_CONFIG })
export class ShareAppConfigMerchantController {
  constructor(private shareAppConfigService: ShareAppConfigMerchantService) {}

  @Get()
  get(@CurrentAuthData() user: User) {
    return this.shareAppConfigService.get(user);
  }

  @Post('refresh')
  refresh(@CurrentAuthData() user: User) {
    return this.shareAppConfigService.refresh(user);
  }

  @Put()
  @AuthorizeMerchant({
    action: Action.UPDATE,
    resource: Resource.SYSTEM_CONFIG,
  })
  update(
    @Body() body: UpdateShareAppConfigMerchantReqDto,
    @CurrentAuthData() user: User,
  ) {
    return this.shareAppConfigService.update(body, user);
  }
}
