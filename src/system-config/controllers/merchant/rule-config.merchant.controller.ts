import { Body, Controller, Get, Put } from '@nestjs/common';
import { ApiExtraModels, ApiTags } from '@nestjs/swagger';
import { User } from '../../../auth/entities/user.entity';
import { PrefixType } from '../../../common/constants/global.constant';
import {
  AuthorizeMerchant,
  CurrentAuthData,
} from '../../../common/decorators/auth.decorator';
import { Action, Resource } from '../../../common/enums/casl.enum';
import { RuleValue } from '../../dtos/common/rule-config.common.dto';
import { UpdateRuleConfigMerchantReqDto } from '../../dtos/merchant/req/rule-config.merchant.req.dto';
import { RuleConfigMerchantService } from '../../services/merchant/rule-config.merchant.service';

@Controller(`${PrefixType.MERCHANT}/rule-config`)
@ApiTags('Rule Config Merchant')
@AuthorizeMerchant({ action: Action.READ, resource: Resource.SYSTEM_CONFIG })
@ApiExtraModels(RuleValue)
export class RuleConfigMerchantController {
  constructor(private ruleConfigMerchantService: RuleConfigMerchantService) {}

  @Get()
  @AuthorizeMerchant({ action: Action.READ, resource: Resource.SYSTEM_CONFIG })
  get(@CurrentAuthData() user: User) {
    return this.ruleConfigMerchantService.get(user);
  }

  @Put()
  @AuthorizeMerchant({
    action: Action.UPDATE,
    resource: Resource.SYSTEM_CONFIG,
  })
  update(
    @Body() body: UpdateRuleConfigMerchantReqDto,
    @CurrentAuthData() user: User,
  ) {
    return this.ruleConfigMerchantService.update(body, user);
  }
}
