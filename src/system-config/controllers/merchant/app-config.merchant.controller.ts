import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Query,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { User } from '../../../auth/entities/user.entity';
import { PrefixType } from '../../../common/constants/global.constant';
import {
  AuthorizeMerchant,
  CurrentAuthData,
} from '../../../common/decorators/auth.decorator';
import { Action, Resource } from '../../../common/enums/casl.enum';
import {
  GetMobileAppVersionMerchantReqDto,
  UpdateMobileAppVersionMerchantReqDto,
} from '../../dtos/merchant/req/app-config.merchant.req.dto';
import { AppConfigMerchantService } from '../../services/merchant/app-config.merchant.service';

@Controller(`${PrefixType.MERCHANT}/app-config`)
@ApiTags('App Config Merchant')
@AuthorizeMerchant({ action: Action.READ, resource: Resource.SYSTEM_CONFIG })
export class AppConfigMerchantController {
  constructor(private appConfigMerchantService: AppConfigMerchantService) {}

  @Get()
  getMobileVersion(
    @Query() query: GetMobileAppVersionMerchantReqDto,
    @CurrentAuthData() user: User,
  ) {
    return this.appConfigMerchantService.getMobileAppVersion(user, query);
  }

  @Patch('/:id')
  updateMobileVersion(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: UpdateMobileAppVersionMerchantReqDto,
    @CurrentAuthData() user: User,
  ) {
    return this.appConfigMerchantService.updateMobileAppVersionByMerchantId(
      id,
      body,
      user,
    );
  }
}
