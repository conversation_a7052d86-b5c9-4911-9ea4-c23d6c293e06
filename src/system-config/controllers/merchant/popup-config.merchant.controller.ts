import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { User } from '../../../auth/entities/user.entity';
import { PrefixType } from '../../../common/constants/global.constant';
import {
  AuthorizeMerchant,
  CurrentAuthData,
} from '../../../common/decorators/auth.decorator';
import { Action, Resource } from '../../../common/enums/casl.enum';
import {
  CreatePopupConfigItemMerchantReqDto,
  UpdatePopupConfigItemMerchantReqDto,
} from '../../dtos/merchant/req/popup-config.merchant.req.dto';
import { PopupConfigMerchantService } from '../../services/merchant/popup-config.merchant.service';

@Controller(`${PrefixType.MERCHANT}/popup-config`)
@ApiTags('Popup Config Merchant')
@AuthorizeMerchant({ action: Action.READ, resource: Resource.SYSTEM_CONFIG })
export class PopupConfigMerchantController {
  constructor(private popupConfigService: PopupConfigMerchantService) {}

  @Get()
  get(@CurrentAuthData() user: User) {
    return this.popupConfigService.get(user);
  }

  @Get(':id')
  getOne(@Param('id') id: string, @CurrentAuthData() user: User) {
    return this.popupConfigService.getOne(id, user);
  }

  @Post()
  @AuthorizeMerchant({
    action: Action.CREATE,
    resource: Resource.SYSTEM_CONFIG,
  })
  create(
    @Body() body: CreatePopupConfigItemMerchantReqDto,
    @CurrentAuthData() user: User,
  ) {
    return this.popupConfigService.create(body, user);
  }

  @Put()
  @AuthorizeMerchant({
    action: Action.UPDATE,
    resource: Resource.SYSTEM_CONFIG,
  })
  update(
    @Body() body: UpdatePopupConfigItemMerchantReqDto,
    @CurrentAuthData() user: User,
  ) {
    return this.popupConfigService.update(body, user);
  }

  @Delete(':id')
  @AuthorizeMerchant({
    action: Action.UPDATE,
    resource: Resource.SYSTEM_CONFIG,
  })
  deleteSingle(@Param('id') id: string, @CurrentAuthData() user: User) {
    return this.popupConfigService.deleteSingle(id, user);
  }
}
