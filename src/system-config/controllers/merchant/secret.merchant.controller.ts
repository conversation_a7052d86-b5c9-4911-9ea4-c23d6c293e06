import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { User } from '../../../auth/entities/user.entity';
import { PrefixType } from '../../../common/constants/global.constant';
import {
  AuthorizeMerchant,
  CurrentAuthData,
} from '../../../common/decorators/auth.decorator';
import { PaginationResponse } from '../../../common/decorators/swagger.decorator';
import { Action, Resource } from '../../../common/enums/casl.enum';
import { SecretResDto } from '../../dtos/common/res/secret.res.dto';
import {
  CreateSecretMerchantReqDto,
  GetListSecretMerchantReqDto,
  UpdateSecretMerchantReqDto,
} from '../../dtos/merchant/req/secret.merchant.req.dto';
import { SecretMerchantService } from '../../services/merchant/secret.merchant.service';

@Controller(`${PrefixType.MERCHANT}/secret`)
@ApiTags('Secret Merchant')
@AuthorizeMerchant({ action: Action.READ, resource: Resource.SECRET })
export class SecretMerchantController {
  constructor(private secretService: SecretMerchantService) {}

  @Get()
  @PaginationResponse(SecretResDto)
  getList(
    @Query() query: GetListSecretMerchantReqDto,
    @CurrentAuthData() user: User,
  ) {
    return this.secretService.getList(query, user);
  }

  @Post()
  @AuthorizeMerchant({ action: Action.CREATE, resource: Resource.SECRET })
  create(
    @Body() body: CreateSecretMerchantReqDto,
    @CurrentAuthData() user: User,
  ) {
    return this.secretService.create(body, user);
  }

  @Put()
  @AuthorizeMerchant({ action: Action.UPDATE, resource: Resource.SECRET })
  update(
    @Body() body: UpdateSecretMerchantReqDto,
    @CurrentAuthData() user: User,
  ) {
    return this.secretService.update(body, user);
  }

  @Delete(':id')
  @AuthorizeMerchant({ action: Action.DELETE, resource: Resource.SECRET })
  delete(@Param('id', ParseIntPipe) id: number, @CurrentAuthData() user: User) {
    return this.secretService.delete(id, user);
  }
}
