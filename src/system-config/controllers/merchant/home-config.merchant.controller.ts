import { Body, Controller, Get, Post, Put } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { User } from '../../../auth/entities/user.entity';
import { PrefixType } from '../../../common/constants/global.constant';
import {
  AuthorizeMerchant,
  CurrentAuthData,
} from '../../../common/decorators/auth.decorator';
import { Action, Resource } from '../../../common/enums/casl.enum';
import { UpdateHomeConfigMerchantReqDto } from '../../dtos/merchant/req/home-config.merchant.req.dto';
import { HomeConfigMerchantService } from '../../services/merchant/home-config.merchant.service';

@Controller(`${PrefixType.MERCHANT}/home-config`)
@ApiTags('Home Config Merchant')
@AuthorizeMerchant({ action: Action.READ, resource: Resource.SYSTEM_CONFIG })
export class HomeConfigMerchantController {
  constructor(private homeConfigService: HomeConfigMerchantService) {}

  @Get()
  get(@CurrentAuthData() user: User) {
    return this.homeConfigService.get(user);
  }

  @Post('refresh')
  refresh(@CurrentAuthData() user: User) {
    return this.homeConfigService.refresh(user);
  }

  @Put()
  @AuthorizeMerchant({
    action: Action.UPDATE,
    resource: Resource.SYSTEM_CONFIG,
  })
  update(
    @Body() body: UpdateHomeConfigMerchantReqDto,
    @CurrentAuthData() user: User,
  ) {
    return this.homeConfigService.update(body, user);
  }
}
