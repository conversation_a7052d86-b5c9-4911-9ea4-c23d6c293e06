import { Body, Controller, Get, Put } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { User } from '../../../auth/entities/user.entity';
import { PrefixType } from '../../../common/constants/global.constant';
import {
  AuthorizeMerchant,
  CurrentAuthData,
} from '../../../common/decorators/auth.decorator';
import { Action, Resource } from '../../../common/enums/casl.enum';
import { UpdateEventConfigMerchantReqDto } from '../../dtos/merchant/req/event-config.merchant.req.dto';
import { EventConfigMerchantService } from '../../services/merchant/event-config.merchant.service';

@Controller(`${PrefixType.MERCHANT}/event-config`)
@ApiTags('Event Config Merchant')
@AuthorizeMerchant({ action: Action.READ, resource: Resource.SYSTEM_CONFIG })
export class EventConfigMerchantController {
  constructor(private eventConfigMerchantService: EventConfigMerchantService) {}

  @Get()
  get(@CurrentAuthData() user: User) {
    return this.eventConfigMerchantService.get(user);
  }

  @Put()
  @AuthorizeMerchant({
    action: Action.UPDATE,
    resource: Resource.SYSTEM_CONFIG,
  })
  update(
    @Body() body: UpdateEventConfigMerchantReqDto,
    @CurrentAuthData() user: User,
  ) {
    return this.eventConfigMerchantService.update(body, user);
  }
}
