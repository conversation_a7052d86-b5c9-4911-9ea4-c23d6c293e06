import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/repositories/base.repositories';
import { I18nPath } from '../../i18n/i18n.generated';
import { Secret } from '../entities/secret.entity';

@Injectable()
export class SecretRepository extends BaseRepository<Secret> {
  entityNameI18nKey: I18nPath;
  constructor(dataSource: DataSource) {
    super(Secret, dataSource);
    this.entityNameI18nKey = 'common.word.secret';
  }

  async createOrUpdate(secret: Secret, userId: number) {
    const secretExit = await this.findOneBy({
      key: secret.key,
      ownerId: userId,
    });

    if (secretExit) {
      secretExit.value = secret.value;
      return this.save(secretExit);
    }
    return this.save(secret);
  }
}
