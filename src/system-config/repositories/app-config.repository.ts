import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/repositories/base.repositories';
import { I18nPath } from '../../i18n/i18n.generated';
import { AppConfig } from '../entities/app-config.entity';

@Injectable()
export class AppConfigRepository extends BaseRepository<AppConfig> {
  entityNameI18nKey: I18nPath;
  constructor(dataSource: DataSource) {
    super(AppConfig, dataSource);
    this.entityNameI18nKey = 'common.word.appConfig';
  }
}
