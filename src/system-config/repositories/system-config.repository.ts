import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/repositories/base.repositories';
import { I18nPath } from '../../i18n/i18n.generated';
import { SystemConfig } from '../entities/system-config.entity';

@Injectable()
export class SystemConfigRepository extends BaseRepository<SystemConfig> {
  entityNameI18nKey: I18nPath;
  constructor(dataSource: DataSource) {
    super(SystemConfig, dataSource);
    this.entityNameI18nKey = 'common.word.systemConfig';
  }
}
