import {
  IsValidEnum,
  IsValidNumber,
} from '../../../../common/decorators/custom-validator.decorator';
import { DeviceType } from '../../../enums/app-config.enum';

export class SaveMobileAppVersionMerchantReqDto {
  @IsValidEnum({ enum: DeviceType, required: false })
  deviceType?: DeviceType;
}
export class GetMobileAppVersionMerchantReqDto extends SaveMobileAppVersionMerchantReqDto {}

export class UpdateMobileAppVersionMerchantReqDto extends SaveMobileAppVersionMerchantReqDto {
  @IsValidNumber({ required: false })
  mobileVersion?: number;
}
