import { IsObject, IsOptional, IsUrl } from 'class-validator';
import {
  IsValidArrayObject,
  IsValidBoolean,
  IsValidDate,
  IsValidNumber,
  IsValidText,
} from '../../../../common/decorators/custom-validator.decorator';

class SavePopupConfigItemMerchantReqDto {
  @IsUrl()
  image: string;

  @IsValidText()
  title: string;

  @IsValidBoolean()
  status: boolean;

  @IsValidText()
  link: string;

  @IsValidDate({ required: false })
  startDate: string;

  @IsValidDate({ required: false })
  endDate: string;

  @IsValidNumber({ min: 1, max: 256 })
  ordinal: number;

  @IsObject()
  @IsOptional()
  params?: any;
}

export class CreatePopupConfigItemMerchantReqDto extends SavePopupConfigItemMerchantReqDto {}

export class UpdatePopupConfigItemMerchantReqDto extends SavePopupConfigItemMerchantReqDto {
  @IsValidText()
  id: string;
}

export class SeedPopupConfigItemMerchantReqDto extends SavePopupConfigItemMerchantReqDto {
  @IsValidText()
  id: string;
}

export class UpdatePopupConfigMerchantReqDto {
  @IsValidArrayObject({ minSize: 1 }, UpdatePopupConfigItemMerchantReqDto)
  popupConfig?: UpdatePopupConfigItemMerchantReqDto[];
}

export class SeedPopupConfigMerchantReqDto {
  @IsValidArrayObject({}, SeedPopupConfigItemMerchantReqDto)
  popupConfig?: SeedPopupConfigItemMerchantReqDto[];
}
