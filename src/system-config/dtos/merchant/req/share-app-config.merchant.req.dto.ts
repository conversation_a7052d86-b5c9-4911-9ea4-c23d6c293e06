import { ApiExtraModels, ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { IsArray } from 'class-validator';
import {
  ShareAppSection,
  ShareAppSectionBanner,
} from '../../common/share-app-config.common.dto';

@ApiExtraModels(ShareAppSectionBanner)
export class UpdateShareAppConfigMerchantReqDto {
  @IsArray()
  @ApiProperty({
    type: 'array',
    items: {
      anyOf: [{ $ref: getSchemaPath(ShareAppSectionBanner) }],
    },
  })
  sections: ShareAppSection[];
}

export class SeedShareAppConfigMerchantReqDto {
  @IsArray()
  sections: ShareAppSection[];
}
