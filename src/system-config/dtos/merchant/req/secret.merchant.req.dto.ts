import {
  <PERSON>ValidEnum,
  IsValidNumber,
} from '../../../../common/decorators/custom-validator.decorator';
import { PaginationReqDto } from '../../../../common/dtos/pagination.dto';
import { SecretKey } from '../../../enums/secret.enum';

export class GetListSecretMerchantReqDto extends PaginationReqDto {
  @IsValidEnum({ enum: SecretKey, required: false })
  key?: SecretKey;
}

export class SaveSecretMerchantReqDto {
  value: any;
}

export class CreateSecretMerchantReqDto extends SaveSecretMerchantReqDto {
  @IsValidEnum({ enum: SecretKey })
  key: SecretKey;
}

export class UpdateSecretMerchantReqDto extends SaveSecretMerchantReqDto {
  @IsValidNumber({ min: 1 })
  id: number;
}
