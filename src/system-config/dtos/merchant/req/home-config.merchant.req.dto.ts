import { ApiExtraModels, ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { IsArray } from 'class-validator';
import { IsValidHomeConfig } from '../../../validators/home-config.validator';
import {
  HomeSection,
  HomeSectionBanner,
  HomeSectionHorizontalProductList1,
  HomeSectionHorizontalProductList2,
  HomeSectionNormalService,
} from '../../common/home-config.common.dto';

@ApiExtraModels(
  HomeSectionBanner,
  HomeSectionNormalService,
  HomeSectionHorizontalProductList1,
  HomeSectionHorizontalProductList2,
)
export class UpdateHomeConfigMerchantReqDto {
  @IsArray()
  @IsValidHomeConfig()
  @ApiProperty({
    type: 'array',
    items: {
      anyOf: [
        { $ref: getSchemaPath(HomeSectionBanner) },
        { $ref: getSchemaPath(HomeSectionNormalService) },
        { $ref: getSchemaPath(HomeSectionHorizontalProductList1) },
        { $ref: getSchemaPath(HomeSectionHorizontalProductList2) },
      ],
    },
  })
  sections: HomeSection[];
}

export class SeedHomeConfigMerchantReqDto {
  @IsArray()
  @IsValidHomeConfig()
  sections: HomeSection[];
}
