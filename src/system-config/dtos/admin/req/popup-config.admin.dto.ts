import {
  IsValidEnum,
  IsValidText,
} from '../../../../common/decorators/custom-validator.decorator';
import { PaginationReqDto } from '../../../../common/dtos/pagination.dto';
import { SystemConfigKey } from '../../../enums/index.num';

export class GetListPopupConfigAdminReqDto extends PaginationReqDto {}

export class PostConfigAdminReqDto {
  @IsValidEnum({ enum: SystemConfigKey })
  key: SystemConfigKey;

  @IsValidText()
  value: string;
}
