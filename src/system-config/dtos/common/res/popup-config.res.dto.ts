import { MerchantResDto } from '../../../../auth/dtos/common/res/merchant.res.dto';
import { Merchant } from '../../../../auth/entities/merchant.entity';
import { PopupConfig } from '../popup-config.common.dto';

class PopupConfigResData {
  popupConfig: PopupConfig;
  merchant?: Merchant;
}

export class PopupConfigResDto {
  popupConfig: PopupConfig;
  merchant: MerchantResDto;

  static mapProperty(dto: PopupConfigResDto, data: PopupConfigResData) {
    dto.popupConfig = data.popupConfig;
  }

  static forCustomer(data: PopupConfigResData) {
    const result = new PopupConfigResDto();
    if (!data) return null;

    this.mapProperty(result, data);

    return result;
  }

  static forMerchant(data: PopupConfigResData) {
    const result = new PopupConfigResDto();
    if (!data) return null;

    this.mapProperty(result, data);

    return result;
  }

  static forAdmin(data: PopupConfigResData) {
    const result = new PopupConfigResDto();
    if (!data) return null;

    this.mapProperty(result, data);
    result.merchant = MerchantResDto.forAdmin({ data: data.merchant });

    return result;
  }
}
