import { MerchantResDto } from '../../../../auth/dtos/common/res/merchant.res.dto';
import { Secret } from '../../../entities/secret.entity';
import { Secret<PERSON>ey } from '../../../enums/secret.enum';

export class SecretResDto {
  id: number;
  key: <PERSON><PERSON><PERSON>;
  merchant: MerchantResDto;

  static mapProperty(dto: SecretResDto, data: Secret) {
    dto.id = data.id;
    dto.key = data.key;
  }

  static forMerchant(data: Secret) {
    const result = new SecretResDto();
    if (!data) return null;

    this.mapProperty(result, data);

    return result;
  }

  static forAdmin(data: Secret) {
    const result = new SecretResDto();
    if (!data) return null;

    this.mapProperty(result, data);
    result.merchant = MerchantResDto.forAdmin({ data: data?.owner?.merchant });

    return result;
  }
}
