import { MerchantResDto } from '../../../../auth/dtos/common/res/merchant.res.dto';
import { Merchant } from '../../../../auth/entities/merchant.entity';
import { EventConfig } from '../event-config.common.dto';

class EventConfigResData {
  eventConfig: EventConfig;
  merchant?: Merchant;
}

export class EventConfigResDto {
  eventConfig: EventConfig;
  merchant: MerchantResDto;

  static mapProperty(dto: EventConfigResDto, data: EventConfigResData) {
    dto.eventConfig = data.eventConfig;
  }

  static forCustomer(data: EventConfigResData) {
    const result = new EventConfigResDto();
    if (!data) return null;

    this.mapProperty(result, data);

    return result;
  }

  static forMerchant(data: EventConfigResData) {
    const result = new EventConfigResDto();
    if (!data) return null;

    this.mapProperty(result, data);

    return result;
  }

  static forAdmin(data: EventConfigResData) {
    const result = new EventConfigResDto();

    if (!data) return null;

    this.mapProperty(result, data);

    result.merchant = MerchantResDto.forAdmin({ data: data.merchant });

    return result;
  }
}
