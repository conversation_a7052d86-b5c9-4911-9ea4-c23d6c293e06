import { MerchantResDto } from '../../../../auth/dtos/common/res/merchant.res.dto';
import { Merchant } from '../../../../auth/entities/merchant.entity';
import { RuleConfig } from '../rule-config.common.dto';

class RuleConfigResData {
  ruleConfig: RuleConfig;
  merchant?: Merchant;
}

export class RuleConfigResDto {
  ruleConfig: RuleConfig;
  merchant: MerchantResDto;

  static mapProperty(dto: RuleConfigResDto, data: RuleConfigResData) {
    dto.ruleConfig = data.ruleConfig;
  }

  static forCustomer(data: RuleConfigResData) {
    const result = new RuleConfigResDto();
    if (!data) return null;

    this.mapProperty(result, data);

    return result;
  }

  static forMerchant(data: RuleConfigResData) {
    const result = new RuleConfigResDto();
    if (!data) return null;

    this.mapProperty(result, data);

    return result;
  }

  static forAdmin(data: RuleConfigResData) {
    const result = new RuleConfigResDto();
    if (!data) return null;

    this.mapProperty(result, data);
    result.merchant = MerchantResDto.forAdmin({ data: data.merchant });

    return result;
  }
}
