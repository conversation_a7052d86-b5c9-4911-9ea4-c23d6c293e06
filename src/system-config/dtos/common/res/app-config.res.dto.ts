import { AppConfig } from '../../../entities/app-config.entity';
import { DeviceType } from '../../../enums/app-config.enum';

export class AppConfigResDto {
  id: number;
  mobileVersion: number;
  deviceType: DeviceType;

  static mapProperty(dto: AppConfigResDto, data: AppConfig) {
    dto.id = data.id;
    dto.mobileVersion = data.mobileVersion;
    dto.deviceType = data.deviceType;
  }

  static forCustomer(data: AppConfig) {
    const result = new AppConfigResDto();
    if (!data) return null;

    this.mapProperty(result, data);

    return result;
  }

  static forMerchant(data: AppConfig) {
    const result = new AppConfigResDto();
    if (!data) return null;

    this.mapProperty(result, data);

    return result;
  }
}
