import { MerchantResDto } from '../../../../auth/dtos/common/res/merchant.res.dto';
import { Merchant } from '../../../../auth/entities/merchant.entity';
import { FeatureConfig } from '../feature-config.common.dto';

class FeatureConfigResData {
  featureConfig: FeatureConfig;
  merchant?: Merchant;
}

export class FeatureConfigResDto {
  featureConfig: FeatureConfig;
  merchant: MerchantResDto;

  static mapProperty(dto: FeatureConfigResDto, data: FeatureConfigResData) {
    dto.featureConfig = data.featureConfig;
  }

  static forCustomer(data: FeatureConfigResData) {
    const result = new FeatureConfigResDto();
    if (!data) return null;

    this.mapProperty(result, data);

    return result;
  }

  static forMerchant(data: FeatureConfigResData) {
    const result = new FeatureConfigResDto();
    if (!data) return null;

    this.mapProperty(result, data);

    return result;
  }

  static forAdmin(data: FeatureConfigResData) {
    const result = new FeatureConfigResDto();
    if (!data) return null;

    this.mapProperty(result, data);
    result.merchant = MerchantResDto.forAdmin({ data: data.merchant });

    return result;
  }
}
