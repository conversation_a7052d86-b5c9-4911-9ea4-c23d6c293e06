import { PopupListItem } from '../popup-config.common.dto';

export class PopupConfigItemResDto {
  image: string;
  title: string;
  status: boolean;
  link: string;
  id: string;
  startDate: string;
  endDate: string;
  ordinal: number;
  params: object;

  static mapProperty(dto: PopupConfigItemResDto, data: PopupListItem) {
    dto.image = data.image;
    dto.title = data.title;
    dto.status = data.status;
    dto.link = data.link;
    dto.id = data.id;
    dto.startDate = data.startDate;
    dto.endDate = data.endDate;
    dto.ordinal = data.ordinal;
    dto.params = data.params;
  }

  static forMerchant(data: PopupListItem) {
    const result = new PopupConfigItemResDto();

    if (!data) return null;

    this.mapProperty(result, data);

    return result;
  }

  static forCustomer(data: PopupListItem) {
    const result = new PopupConfigItemResDto();

    if (!data) return null;

    this.mapProperty(result, data);

    return result;
  }
}
