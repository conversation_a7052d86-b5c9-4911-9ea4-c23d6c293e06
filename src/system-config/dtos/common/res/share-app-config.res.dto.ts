import { MerchantResDto } from '../../../../auth/dtos/common/res/merchant.res.dto';
import { PartialNonFunctionProperties } from '../../../../common/types/utils.type';
import { ShareAppSection } from '../share-app-config.common.dto';

export class ShareAppConfigResDto {
  sections: ShareAppSection[];
  merchant: MerchantResDto;

  static mapProperty(
    dto: ShareAppConfigResDto,
    data: PartialNonFunctionProperties<ShareAppConfigResDto>,
  ) {
    dto.sections = data.sections;
  }

  static forCustomer(data: PartialNonFunctionProperties<ShareAppConfigResDto>) {
    const result = new ShareAppConfigResDto();
    if (!data) return null;

    this.mapProperty(result, data);

    return result;
  }

  static forMerchant(data: PartialNonFunctionProperties<ShareAppConfigResDto>) {
    const result = new ShareAppConfigResDto();
    if (!data) return null;

    this.mapProperty(result, data);

    return result;
  }

  static forAdmin(data: PartialNonFunctionProperties<ShareAppConfigResDto>) {
    const result = new ShareAppConfigResDto();
    if (!data) return null;

    this.mapProperty(result, data);

    return result;
  }
}
