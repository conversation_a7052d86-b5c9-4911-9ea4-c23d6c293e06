import { IsObject, IsOptional, IsUrl } from 'class-validator';
import {
  IsValidBoolean,
  IsValidDate,
  IsValidNumber,
  IsValidText,
} from '../../../common/decorators/custom-validator.decorator';

export class PopupListItem {
  @IsUrl({}, { message: 'system-config.common.invalidImageUrl' })
  image: string;

  @IsValidText({ message: 'system-config.common.invalidId' })
  id: string;

  @IsValidText({
    required: false,
    message: 'system-config.common.invalidTitle',
  })
  title: string;

  @IsValidBoolean({
    required: true,
    message: 'system-config.common.invalidStatus',
  })
  status: boolean;

  @IsValidText({
    required: false,
    message: 'system-config.common.invalidLink',
  })
  link: string;

  @IsValidDate({ required: false, message: 'system-config.common.invalidLink' })
  startDate: string;

  @IsValidDate({ required: false, message: 'system-config.common.invalidLink' })
  endDate: string;

  @IsValidNumber({
    min: 1,
    max: 256,
    message: 'system-config.popupConfig.popupConfigOrdinalAlready',
  })
  ordinal: number;

  @IsObject()
  @IsOptional()
  params?: any;
}

export type PopupConfig = PopupListItem[];
