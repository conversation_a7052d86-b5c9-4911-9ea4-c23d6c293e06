import {
  IsValidBoolean,
  IsValidText,
} from '../../../common/decorators/custom-validator.decorator';
import {
  RuleConfigType,
  RuleCustomerRankType,
  RuleDateType,
  RuleDefaultPointType,
  RuleLimitedTurnType,
} from '../../enums/rule-config.enum';

export type RuleCustomerRank = {
  [key in keyof typeof RuleCustomerRankType]: number;
};

export type RuleDefaultPoint = {
  [key in keyof typeof RuleDefaultPointType]: number;
};

export type RuleLimitedTurn = {
  [key in keyof typeof RuleLimitedTurnType]: number;
};

export type RuleDate = {
  [key in keyof typeof RuleDateType]: string;
};

export class RuleValue {
  @IsValidText({ required: false, message: 'system-config.common.invalidDesc' })
  desc: string;

  @IsValidBoolean({ message: 'system-config.common.invalidStatus' })
  status: boolean;

  values: RuleCustomerRank | RuleDefaultPoint | RuleLimitedTurn | RuleDate;
}

export type RuleConfig = {
  [key in keyof typeof RuleConfigType]: RuleValue;
};
