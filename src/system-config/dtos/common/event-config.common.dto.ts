import {
  IsValidBoolean,
  IsValidText,
} from '../../../common/decorators/custom-validator.decorator';
import { EventConfigType } from '../../enums/event-config.enum';

class EventConfigItem {
  @IsValidText({ required: false, message: 'system-config.common.invalidDesc' })
  desc: string;

  @IsValidBoolean({ message: 'system-config.common.invalidStatus' })
  status: boolean;

  @IsValidText({ message: 'system-config.common.invalidStartDate' })
  startDate: string;

  @IsValidText({ message: 'system-config.common.invalidEndDate' })
  endDate: string;
}

export type EventConfig = {
  [key in keyof typeof EventConfigType]: EventConfigItem;
};
