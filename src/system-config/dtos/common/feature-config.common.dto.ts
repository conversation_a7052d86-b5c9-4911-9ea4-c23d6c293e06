import {
  IsValidBoolean,
  IsValidText,
} from '../../../common/decorators/custom-validator.decorator';
import { FeatureConfigType } from '../../enums/feature-config.enum';

class FeatureConfigItem {
  @IsValidText({ required: false, message: 'system-config.common.invalidDesc' })
  desc: string;

  @IsValidBoolean({ message: 'system-config.common.invalidStatus' })
  status: boolean;
}

export type FeatureConfig = {
  [key in keyof typeof FeatureConfigType]: FeatureConfigItem;
};
