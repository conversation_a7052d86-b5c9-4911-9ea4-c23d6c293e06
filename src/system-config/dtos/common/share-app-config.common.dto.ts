import { IsObject, IsOptional, IsUrl } from 'class-validator';
import {
  IsValidArrayObject,
  IsValidEnum,
  IsValidNumber,
  IsValidText,
} from '../../../common/decorators/custom-validator.decorator';
import { MobileRouteType } from '../../../common/enums/app.enum';

export class ShareAppSectionBannerDataItem {
  @IsUrl({}, { message: 'system-config.common.invalidImage' })
  image: string;

  @IsValidNumber({ message: 'system-config.common.invalidImageId' })
  imageId: number;

  @IsValidText({ required: false, message: 'system-config.common.invalidLink' })
  link: string;

  @IsObject()
  @IsOptional()
  params?: any;

  @IsValidEnum({ enum: MobileRouteType })
  typeRoute: MobileRouteType;
}

export class ShareAppSectionBanner {
  @IsValidArrayObject(
    {
      required: true,
      minSize: 1,
      message: 'system-config.homeConfig.common.invalidData',
    },
    ShareAppSectionBannerDataItem,
  )
  data: ShareAppSectionBannerDataItem[];
}

export type ShareAppSection = ShareAppSectionBanner;
