import { Equals, IsObject, IsOptional, IsUrl } from 'class-validator';

import {
  IsValidArrayObject,
  IsValidNumber,
  IsValidObject,
  IsValidText,
} from '../../../common/decorators/custom-validator.decorator';

import { HomeSectionType } from '../../enums/home-config.enum';

export class HomeSectionBannerDataItem {
  @IsUrl({}, { message: 'system-config.common.invalidImage' })
  image: string;

  @IsValidNumber({ message: 'system-config.common.invalidImageId' })
  imageId: number;

  @IsValidText({ required: false, message: 'system-config.common.invalidLink' })
  link: string;

  @IsObject()
  @IsOptional()
  params?: any;
}

export class HomeSectionBanner {
  @Equals(HomeSectionType.BANNER)
  type: HomeSectionType.BANNER;

  @IsValidArrayObject(
    {
      required: true,
      minSize: 1,
      message: 'system-config.homeConfig.common.invalidData',
    },
    HomeSectionBannerDataItem,
  )
  data: HomeSectionBannerDataItem[];
}

class HomeSectionNormalServiceDataItem {
  @IsUrl({}, { message: 'system-config.common.invalidImage' })
  image: string;

  @IsValidText({ required: false, message: 'system-config.common.invalidLink' })
  link: string;

  @IsValidNumber({ message: 'system-config.common.invalidImageId' })
  imageId: number;

  @IsValidText({ message: 'system-config.common.invalidName' })
  name: string;

  @IsObject()
  @IsOptional()
  params?: any;
}

export class HomeSectionNormalService {
  @Equals(HomeSectionType.NORMAL_SERVICE)
  type: HomeSectionType.NORMAL_SERVICE;

  @IsValidText({ message: 'system-config.common.invalidTitle' })
  title: string;

  @IsValidArrayObject(
    {
      required: true,
      minSize: 1,
      message: 'system-config.homeConfig.common.invalidData',
    },
    HomeSectionNormalServiceDataItem,
  )
  data: HomeSectionNormalServiceDataItem[];
}

class HomeSectionHorizontalProductListData {
  @IsValidNumber({ min: 1, message: 'system-config.common.invalidId' })
  categoryId: number;

  @IsValidNumber({ min: 1, message: 'system-config.common.invalidMaxLength' })
  maxLength: number;

  products: any;
}

export class HomeSectionHorizontalProductList1 {
  @Equals(HomeSectionType.HORIZONTAL_PRODUCT_LIST_1)
  type: HomeSectionType.HORIZONTAL_PRODUCT_LIST_1;

  @IsValidText({ message: 'system-config.common.invalidTitle' })
  title: string;

  @IsValidObject({
    object: HomeSectionHorizontalProductListData,
    message: 'system-config.homeConfig.common.invalidData',
  })
  data: HomeSectionHorizontalProductListData;
}

export class HomeSectionHorizontalProductList2 {
  @Equals(HomeSectionType.HORIZONTAL_PRODUCT_LIST_2)
  type: HomeSectionType.HORIZONTAL_PRODUCT_LIST_2;

  @IsValidText({ message: 'system-config.common.invalidTitle' })
  title: string;

  @IsValidObject({
    object: HomeSectionHorizontalProductListData,
    message: 'system-config.homeConfig.common.invalidData',
  })
  data: HomeSectionHorizontalProductListData;
}

export type HomeSection =
  | HomeSectionBanner
  | HomeSectionNormalService
  | HomeSectionHorizontalProductList1
  | HomeSectionHorizontalProductList2;
