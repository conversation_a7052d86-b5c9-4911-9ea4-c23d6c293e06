export enum SystemConfigKey {
  HOME_CONFIG = 'HOME_CONFIG',
  FEATURE_CONFIG = 'FEATURE_CONFIG',
  RULE_CONFIG = 'RULE_CONFIG',
  EVENT_CONFIG = 'EVENT_CONFIG',
  FEATURE_RULE_CONFIG = 'FEATURE_RULE_CONFIG',
  POPUP_CONFIG = 'POPUP_CONFIG',
  LIMIT_NUMBER_OF_SCAN_FAIL_IN_DAY = 'LIMIT_NUMBER_OF_SCAN_FAIL_IN_DAY',
  LIMIT_NUMBER_OF_SCAN_SUCCESS_IN_DAY = 'LIMIT_NUMBER_OF_SCAN_SUCCESS_IN_DAY',
  NUMBER_DAY_SCAN_FAIL_BLOCK_ACCOUNT = 'NUMBER_DAY_SCAN_FAIL_BLOCK_ACCOUNT',
  LIMIT_NUMBER_OF_GEN_SPOON_CODE_IN_MONTH = 'LIMIT_NUMBER_OF_GEN_SPOON_CODE_IN_MONTH',
  SHARE_APP_CONFIG = 'SHARE_APP_CONFIG',
}
