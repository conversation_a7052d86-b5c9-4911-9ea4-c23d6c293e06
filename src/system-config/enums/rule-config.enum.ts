export enum RuleConfigType {
  GAME_LUCKY_WHEEL_POINT = 'GAME_LUCKY_WHEEL_POINT',
  BIRTHDAY_NOTIFICATION_POINT = 'BIRTHDAY_NOTIFICATION_POINT',
  FIRST_TIME_ADD_POINT = 'FIRST_TIME_ADD_POINT',
  REFERRAL_APP_POINT = 'REFERRAL_APP_POINT',
  LIMITED_ADD_POINT_TURN = 'LIMITED_ADD_POINT_TURN',
  BLOCK_USER_WRONG_RULE_TURN = 'BLOCK_USER_WRONG_RULE_TURN',
  RESET_POINT_TIME = 'RESET_POINT_TIME',
  RESET_RANK_TIME = 'RESET_RANK_TIME',
  BLOCK_ADD_POINT_WRONG_RULE_TURN = 'BLOCK_ADD_POINT_WRONG_RULE_TURN',
}

export enum RuleCustomerRankType {
  MEMBER_POINT = 'MEMBER',
  TITAN_POINT = 'TITAN',
  GOLD_POINT = 'GOLD',
  PLATINUM_POINT = 'PLATINUM',
}

export enum RuleDefaultPointType {
  DEFAULT_POINT = 'DEFAULT_POINT',
}

export enum RuleLimitedTurnType {
  LIMITED_TURN = 'LIMITED_TURN',
}

export enum RuleDateType {
  RULE_DATE = 'RULE_DATE',
}
