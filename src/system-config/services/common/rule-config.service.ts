import { Injectable } from '@nestjs/common';
import { RuleConfig } from '../../dtos/common/rule-config.common.dto';
import { SystemConfigKey } from '../../enums/index.num';
import { RuleConfigType } from '../../enums/rule-config.enum';
import { SystemConfigRepository } from '../../repositories/system-config.repository';

@Injectable()
export class RuleConfigService {
  constructor(private systemConfigRepo: SystemConfigRepository) {}

  async isRuleTurnedOn(type: RuleConfigType): Promise<boolean> {
    try {
      const ruleConfig =
        await this.systemConfigRepo.findOneByOrThrowNotFoundExc({
          key: SystemConfigKey.RULE_CONFIG,
        });

      if (!ruleConfig) return false;

      const ruleConfigItem = (ruleConfig.value as RuleConfig)[type];

      if (ruleConfigItem?.status) return true;

      return false;
    } catch (error) {
      return false;
    }
  }
}
