import { Injectable } from '@nestjs/common';
import { FeatureConfig } from '../../dtos/common/feature-config.common.dto';
import { FeatureConfigType } from '../../enums/feature-config.enum';
import { SystemConfigKey } from '../../enums/index.num';
import { SystemConfigRepository } from '../../repositories/system-config.repository';

@Injectable()
export class FeatureConfigService {
  constructor(private systemConfigRepo: SystemConfigRepository) {}

  async isFeatureTurnedOn(
    type: FeatureConfigType,
    merchantUserId: number,
  ): Promise<boolean> {
    try {
      const featureConfig =
        await this.systemConfigRepo.findOneByOrThrowNotFoundExc({
          key: SystemConfigKey.FEATURE_CONFIG,
          ownerId: merchantUserId,
        });

      if (!featureConfig) return false;

      const featureConfigItem = (featureConfig.value as FeatureConfig)[type];
      if (!featureConfigItem) return false;

      return featureConfigItem.status;
    } catch (error) {
      return false;
    }
  }
}
