import { Injectable } from '@nestjs/common';
import { User } from '../../../auth/entities/user.entity';
import { MerchantRepository } from '../../../auth/repositories/merchant.repository';
import { NotFoundExc } from '../../../common/exceptions/custom.exception';
import { RuleConfigResDto } from '../../dtos/common/res/rule-config.res.dto';
import {
  SeedRuleConfigMerchantReqDto,
  UpdateRuleConfigMerchantReqDto,
} from '../../dtos/merchant/req/rule-config.merchant.req.dto';
import { SystemConfigKey } from '../../enums/index.num';
import { SystemConfigRepository } from '../../repositories/system-config.repository';

@Injectable()
export class RuleConfigMerchantService {
  constructor(
    private systemConfigRepo: SystemConfigRepository,
    private merchantRepo: MerchantRepository,
  ) {}

  async get(user: User) {
    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );

    const systemConfig =
      await this.systemConfigRepo.findOneByOrThrowNotFoundExc({
        ownerId: rootMerchantUserId,
        key: SystemConfigKey.RULE_CONFIG,
      });

    if (!systemConfig)
      throw new NotFoundExc({
        message: 'system-config.ruleConfig.ruleConfigNotFound',
      });

    return RuleConfigResDto.forMerchant({
      ruleConfig: systemConfig.value,
    });
  }

  async update(dto: UpdateRuleConfigMerchantReqDto, user: User) {
    const { ruleConfig } = dto;

    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );

    let systemConfig = await this.systemConfigRepo.findOneBy({
      key: SystemConfigKey.RULE_CONFIG,
      ownerId: rootMerchantUserId,
    });

    if (!systemConfig) {
      systemConfig = this.systemConfigRepo.create({
        ownerId: rootMerchantUserId,
        key: SystemConfigKey.RULE_CONFIG,
      });
    }

    systemConfig.value = ruleConfig;

    await this.systemConfigRepo.save(systemConfig);

    return RuleConfigResDto.forMerchant({ ruleConfig });
  }

  async seed(dto: SeedRuleConfigMerchantReqDto, ownerId: number) {
    const { ruleConfig } = dto;

    const systemConfig = this.systemConfigRepo.create({
      ownerId,
      key: SystemConfigKey.RULE_CONFIG,
    });

    systemConfig.value = ruleConfig;

    await this.systemConfigRepo.save(systemConfig);
  }
}
