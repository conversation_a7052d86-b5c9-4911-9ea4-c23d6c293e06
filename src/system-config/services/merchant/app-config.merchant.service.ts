import { Injectable } from '@nestjs/common';
import { User } from '../../../auth/entities/user.entity';
import { MerchantRepository } from '../../../auth/repositories/merchant.repository';
import { AppConfigResDto } from '../../dtos/common/res/app-config.res.dto';
import {
  GetMobileAppVersionMerchantReqDto,
  UpdateMobileAppVersionMerchantReqDto,
} from '../../dtos/merchant/req/app-config.merchant.req.dto';
import { AppConfigRepository } from '../../repositories/app-config.repository';

@Injectable()
export class AppConfigMerchantService {
  constructor(
    private appConfigRepo: AppConfigRepository,
    private merchantRepo: MerchantRepository,
  ) {}

  async getMobileAppVersion(
    user: User,
    dto: GetMobileAppVersionMerchantReqDto,
  ) {
    const { deviceType } = dto;
    const rootMerchantId = await this.merchantRepo.getRootMerchantId(
      user.merchant,
    );

    const appConfigs = await this.appConfigRepo.find({
      where: {
        ownerId: rootMerchantId,
        ...(deviceType && { deviceType }),
      },
    });

    const result = appConfigs.map((item) => {
      return AppConfigResDto.forMerchant(item);
    });

    return result;
  }

  async updateMobileAppVersionByMerchantId(
    id: number,
    dto: UpdateMobileAppVersionMerchantReqDto,
    user: User,
  ) {
    const { deviceType, mobileVersion } = dto;
    const rootMerchantId = await this.merchantRepo.getRootMerchantId(
      user.merchant,
    );
    const appConfig = await this.appConfigRepo.findOneByOrThrowNotFoundExc({
      id: id,
      ownerId: rootMerchantId,
      deviceType: deviceType,
    });
    const result = await this.appConfigRepo.update(id, {
      deviceType: deviceType,
      mobileVersion: mobileVersion,
    });
    return result;
  }
}
