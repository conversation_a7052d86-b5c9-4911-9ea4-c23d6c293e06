import { Injectable } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { User } from '../../../auth/entities/user.entity';
import { MerchantRepository } from '../../../auth/repositories/merchant.repository';
import {
  BadRequestExc,
  NotFoundExc,
} from '../../../common/exceptions/custom.exception';
import { PopupListItem } from '../../dtos/common/popup-config.common.dto';
import { PopupConfigItemResDto } from '../../dtos/common/res/popup-config-item.res.dto';
import { PopupConfigResDto } from '../../dtos/common/res/popup-config.res.dto';
import {
  CreatePopupConfigItemMerchantReqDto,
  SeedPopupConfigMerchantReqDto,
  UpdatePopupConfigItemMerchantReqDto,
  UpdatePopupConfigMerchantReqDto,
} from '../../dtos/merchant/req/popup-config.merchant.req.dto';
import { SystemConfig } from '../../entities/system-config.entity';
import { SystemConfigKey } from '../../enums/index.num';
import { SystemConfigRepository } from '../../repositories/system-config.repository';

@Injectable()
export class PopupConfigMerchantService {
  constructor(
    private merchantRepo: MerchantRepository,
    private systemConfigRepo: SystemConfigRepository,
  ) {}

  async get(user: User) {
    const systemConfig = await this.getOrCreateIfNotExist(user);

    return PopupConfigResDto.forMerchant({
      popupConfig: systemConfig.value,
    });
  }

  async getOne(id: string, user: User) {
    const systemConfig = await this.getOrCreateIfNotExist(user);

    const popupConfigItem = systemConfig.value?.find(
      (popupConfigItem: PopupListItem) => popupConfigItem.id === id,
    );

    if (!popupConfigItem)
      throw new BadRequestExc({
        message: 'system-config.popupConfig.popupConfigItemNotFound',
      });

    return PopupConfigItemResDto.forMerchant(popupConfigItem);
  }

  async create(dto: CreatePopupConfigItemMerchantReqDto, user: User) {
    const newPopupConfigItem = {
      id: uuidv4(),
      ...dto,
    };

    const systemConfig = await this.getOrCreateIfNotExist(user);

    //check ordinal item
    if (this.checkOrdinal(systemConfig.value, newPopupConfigItem))
      throw new BadRequestExc({
        message: 'system-config.popupConfig.popupConfigOrdinalAlready',
      });

    //add new value to popup config
    systemConfig.value = [...systemConfig.value, newPopupConfigItem];

    await this.systemConfigRepo.save(systemConfig);

    return PopupConfigItemResDto.forMerchant(newPopupConfigItem);
  }

  async update(dto: UpdatePopupConfigItemMerchantReqDto, user: User) {
    const { id } = dto;
    let isPopupItemExist = false;

    const systemConfig = await this.getOrCreateIfNotExist(user);

    //Create and update new value array of popup config
    const updatePopupConfig = systemConfig.value.map(
      (item: UpdatePopupConfigItemMerchantReqDto) => {
        if (item?.id === id) {
          isPopupItemExist = true;
          return dto;
        }
        return item;
      },
    );

    if (!isPopupItemExist)
      throw new NotFoundExc({
        message: 'system-config.popupConfig.popupConfigItemNotFound',
      });

    if (this.checkOrdinal(systemConfig.value, dto, false))
      throw new BadRequestExc({
        message: 'system-config.popupConfig.popupConfigOrdinalAlready',
      });

    systemConfig.value = updatePopupConfig;

    await this.systemConfigRepo.save(systemConfig);

    return PopupConfigItemResDto.forMerchant(dto);
  }

  async deleteSingle(id: string, user: User) {
    const systemConfig = await this.getOrCreateIfNotExist(user);

    //Remove item
    const newPopupConfig = systemConfig.value.filter(
      (item: UpdatePopupConfigItemMerchantReqDto) => {
        if (item.id !== id) return item;
      },
    );

    systemConfig.value = newPopupConfig;

    await this.systemConfigRepo.save(systemConfig);
  }

  async seed(dto: SeedPopupConfigMerchantReqDto, ownerId: number) {
    const { popupConfig } = dto;

    const systemConfig = this.systemConfigRepo.create({
      ownerId: ownerId,
      key: SystemConfigKey.POPUP_CONFIG,
    });

    //add data
    systemConfig.value = popupConfig;

    await this.systemConfigRepo.save(systemConfig);
  }

  //To turn off other popup config item
  private turnOffOtherPopup(
    data: UpdatePopupConfigMerchantReqDto,
    id?: string,
  ) {
    const newData = data.popupConfig.map(
      (item: UpdatePopupConfigItemMerchantReqDto) => {
        if (item.status === true && item.id !== id)
          return {
            ...item,
            status: false,
          };
        return item;
      },
    );
    return newData;
  }

  //Check oderItem
  private checkOrdinal(
    popupConfig: PopupListItem[],
    popupConfigItem: PopupListItem,
    checkItsOwnId = true,
  ): boolean {
    let result = false;

    if (checkItsOwnId) {
      popupConfig.forEach((item) => {
        if (item.ordinal === popupConfigItem.ordinal) return (result = true);
      });
    } else {
      popupConfig.forEach((item) => {
        if (
          item.ordinal === popupConfigItem.ordinal &&
          item.id !== popupConfigItem.id
        )
          return (result = true);
      });
    }

    return result;
  }

  //Get Popup Config list or create if dont have popup config
  private async getOrCreateIfNotExist(user: User): Promise<SystemConfig> {
    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );

    let systemConfig = await this.systemConfigRepo.findOneBy({
      key: SystemConfigKey.POPUP_CONFIG,
      ownerId: rootMerchantUserId,
    });

    if (!systemConfig) {
      systemConfig = this.systemConfigRepo.create({
        ownerId: rootMerchantUserId,
        key: SystemConfigKey.POPUP_CONFIG,
      });
    }

    //create new array jsonb
    if (!systemConfig.value) systemConfig.value = [];

    return systemConfig;
  }
}
