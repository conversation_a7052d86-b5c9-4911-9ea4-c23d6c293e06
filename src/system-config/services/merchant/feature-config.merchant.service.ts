import { Injectable } from '@nestjs/common';
import { User } from '../../../auth/entities/user.entity';
import { MerchantRepository } from '../../../auth/repositories/merchant.repository';
import { NotFoundExc } from '../../../common/exceptions/custom.exception';
import { FeatureConfigResDto } from '../../dtos/common/res/feature-config.res.dto';
import {
  SeedFeatureConfigMerchantReqDto,
  UpdateFeatureConfigMerchantReqDto,
} from '../../dtos/merchant/req/feature-config.merchant.req.dto';
import { SystemConfigKey } from '../../enums/index.num';
import { SystemConfigRepository } from '../../repositories/system-config.repository';

@Injectable()
export class FeatureConfigMerchantService {
  constructor(
    private systemConfigRepo: SystemConfigRepository,
    private merchantRepo: MerchantRepository,
  ) {}

  async get(user: User) {
    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );

    const systemConfig =
      await this.systemConfigRepo.findOneByOrThrowNotFoundExc({
        ownerId: rootMerchantUserId,
        key: SystemConfigKey.FEATURE_CONFIG,
      });

    if (!systemConfig)
      throw new NotFoundExc({
        message: 'system-config.featureConfig.featureConfigNotFound',
      });

    return FeatureConfigResDto.forMerchant({
      featureConfig: systemConfig.value,
    });
  }

  async update(dto: UpdateFeatureConfigMerchantReqDto, user: User) {
    const { featureConfig } = dto;

    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );

    let systemConfig = await this.systemConfigRepo.findOneBy({
      key: SystemConfigKey.FEATURE_CONFIG,
      ownerId: rootMerchantUserId,
    });
    if (!systemConfig) {
      systemConfig = this.systemConfigRepo.create({
        ownerId: rootMerchantUserId,
        key: SystemConfigKey.FEATURE_CONFIG,
      });
    }

    systemConfig.value = featureConfig;

    await this.systemConfigRepo.save(systemConfig);

    return FeatureConfigResDto.forMerchant({ featureConfig });
  }

  async seed(dto: SeedFeatureConfigMerchantReqDto, ownerId: number) {
    const { featureConfig } = dto;

    const systemConfig = this.systemConfigRepo.create({
      key: SystemConfigKey.FEATURE_CONFIG,
      ownerId,
    });

    systemConfig.value = featureConfig;

    await this.systemConfigRepo.save(systemConfig);
  }
}
