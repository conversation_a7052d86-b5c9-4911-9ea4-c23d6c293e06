import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { runOnTransactionCommit, Transactional } from 'typeorm-transactional';
import { User } from '../../../auth/entities/user.entity';
import { MerchantRepository } from '../../../auth/repositories/merchant.repository';
import { ReCacheIntDto } from '../../../cache/dtos/internal/cache.int.dto';
import { CacheKey } from '../../../cache/enums/cache.enum';
import { CacheService } from '../../../cache/services/cache.service';
import { EventName } from '../../../common/enums/event.enum';
import { NotFoundExc } from '../../../common/exceptions/custom.exception';
import { UtilService } from '../../../utils/services/util.service';
import { ShareAppConfigResDto } from '../../dtos/common/res/share-app-config.res.dto';
import {
  SeedShareAppConfigMerchantReqDto,
  UpdateShareAppConfigMerchantReqDto,
} from '../../dtos/merchant/req/share-app-config.merchant.req.dto';
import { SystemConfigKey } from '../../enums/index.num';
import { SystemConfigRepository } from '../../repositories/system-config.repository';
@Injectable()
export class ShareAppConfigMerchantService {
  constructor(
    private systemConfigRepo: SystemConfigRepository,
    private cacheService: CacheService,
    private eventEmitter: EventEmitter2,
    private merchantRepo: MerchantRepository,
    private utilService: UtilService,
  ) {}

  @Transactional()
  async get(user: User) {
    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );
    const rootMerchantId = await this.merchantRepo.getRootMerchantId(
      user.merchant,
    );

    const shareAppConfig = await this.systemConfigRepo.findOneBy({
      key: SystemConfigKey.SHARE_APP_CONFIG,
      ownerId: rootMerchantUserId,
    });

    if (!shareAppConfig)
      throw new NotFoundExc({
        message: 'system-config.homeConfig.common.homeConfigNotFound',
      });

    return ShareAppConfigResDto.forMerchant({
      sections: shareAppConfig.value,
    });
  }

  @Transactional()
  async update(dto: UpdateShareAppConfigMerchantReqDto, user: User) {
    const { sections } = dto;

    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );
    const rootMerchantId = await this.merchantRepo.getRootMerchantId(
      user.merchant,
    );

    let shareAppConfig = await this.systemConfigRepo.findOneBy({
      key: SystemConfigKey.SHARE_APP_CONFIG,
      ownerId: rootMerchantUserId,
    });
    if (!shareAppConfig) {
      shareAppConfig = this.systemConfigRepo.create({
        key: SystemConfigKey.SHARE_APP_CONFIG,
        ownerId: rootMerchantUserId,
      });
    }

    shareAppConfig.value = sections;

    await this.systemConfigRepo.save(shareAppConfig);

    runOnTransactionCommit(() => {
      this.eventEmitter.emit(EventName.CACHE_STALED, {
        merchantId: rootMerchantId,
        key: CacheKey.SHARE_APP_CONFIG,
      } as ReCacheIntDto);
    });

    return this.get(user);
  }

  @Transactional()
  async refresh(user: User) {
    const rootMerchantId = await this.merchantRepo.getRootMerchantId(
      user.merchant,
    );

    runOnTransactionCommit(() => {
      this.eventEmitter.emitAsync(EventName.CACHE_STALED, {
        merchantId: rootMerchantId,
        key: CacheKey.SHARE_APP_CONFIG,
      } as ReCacheIntDto);
    });
  }

  @Transactional()
  async seed(dto: SeedShareAppConfigMerchantReqDto, userId: number) {
    const { sections } = dto;

    const shareAppConfig = this.systemConfigRepo.create({
      key: SystemConfigKey.SHARE_APP_CONFIG,
      ownerId: userId,
    });

    shareAppConfig.value = sections;

    await this.systemConfigRepo.save(shareAppConfig);
  }
}
