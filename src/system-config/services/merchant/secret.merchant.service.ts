import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { paginate, Pagination } from 'nestjs-typeorm-paginate';
import { Transactional } from 'typeorm-transactional';
import { User } from '../../../auth/entities/user.entity';
import { MerchantRepository } from '../../../auth/repositories/merchant.repository';
import { GlobalConfig } from '../../../common/config/global.config';
import { ConflictExc } from '../../../common/exceptions/custom.exception';
import { SecretResDto } from '../../dtos/common/res/secret.res.dto';
import {
  CreateSecretMerchantReqDto,
  GetListSecretMerchantReqDto,
  UpdateSecretMerchantReqDto,
} from '../../dtos/merchant/req/secret.merchant.req.dto';
import { SecretRepository } from '../../repositories/secret.repository';

@Injectable()
export class SecretMerchantService {
  constructor(
    private secretRepo: SecretRepository,
    private configService: ConfigService<GlobalConfig>,
    private merchantRepo: MerchantRepository,
  ) {}

  @Transactional()
  async getList(dto: GetListSecretMerchantReqDto, user: User) {
    const { key, limit, page } = dto;

    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );

    const qb = this.secretRepo
      .createQueryBuilder('s')
      .where('s.ownerId = :ownerId', { ownerId: rootMerchantUserId })
      .orderBy('s.id', 'ASC');

    if (key) qb.andWhere('s.key = :key', { key });

    const { items, meta } = await paginate(qb, { limit, page });

    const secrets = items.map((item) => SecretResDto.forMerchant(item));

    return new Pagination(secrets, meta);
  }

  @Transactional()
  async create(dto: CreateSecretMerchantReqDto, user: User) {
    const { key, value } = dto;

    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );

    let secret = await this.secretRepo.findOneBy({
      key,
      ownerId: rootMerchantUserId,
    });
    if (secret) throw new ConflictExc({ message: 'common' });

    const dbKey = this.configService.getOrThrow('database.secretKey');

    secret = this.secretRepo.create({
      key,
      value: () => `PGP_SYM_ENCRYPT('${value}','${dbKey}')`,
      ownerId: rootMerchantUserId,
    });
    await this.secretRepo.save(secret);

    return SecretResDto.forMerchant(secret);
  }

  @Transactional()
  async update(dto: UpdateSecretMerchantReqDto, user: User) {
    const { id, value } = dto;

    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );

    const secret = await this.secretRepo.findOneByOrThrowNotFoundExc({
      id,
      ownerId: rootMerchantUserId,
    });
    const dbKey = this.configService.getOrThrow('database.secretKey');

    secret.value = () => `PGP_SYM_ENCRYPT('${value}','${dbKey}')`;
    await this.secretRepo.save(secret);

    return SecretResDto.forMerchant(secret);
  }

  @Transactional()
  async delete(id: number, user: User) {
    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );

    await this.secretRepo.findOneByOrThrowNotFoundExc({
      id,
      ownerId: rootMerchantUserId,
    });
    await this.secretRepo.softDelete({ id });
  }
}
