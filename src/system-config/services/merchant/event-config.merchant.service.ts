import { Injectable } from '@nestjs/common';
import { User } from '../../../auth/entities/user.entity';
import { MerchantRepository } from '../../../auth/repositories/merchant.repository';
import { NotFoundExc } from '../../../common/exceptions/custom.exception';
import { EventConfigResDto } from '../../dtos/common/res/event-config.res.dto';
import {
  SeedEventConfigMerchantReqDto,
  UpdateEventConfigMerchantReqDto,
} from '../../dtos/merchant/req/event-config.merchant.req.dto';
import { SystemConfigKey } from '../../enums/index.num';
import { SystemConfigRepository } from '../../repositories/system-config.repository';

@Injectable()
export class EventConfigMerchantService {
  constructor(
    private systemConfigRepo: SystemConfigRepository,
    private merchantRepo: MerchantRepository,
  ) {}

  async get(user: User) {
    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );

    const systemConfig =
      await this.systemConfigRepo.findOneByOrThrowNotFoundExc({
        ownerId: rootMerchantUserId,
        key: SystemConfigKey.EVENT_CONFIG,
      });

    if (!systemConfig)
      throw new NotFoundExc({
        message: 'system-config.eventConfig.eventConfigNotFound',
      });

    return EventConfigResDto.forMerchant({ eventConfig: systemConfig.value });
  }

  async update(dto: UpdateEventConfigMerchantReqDto, user: User) {
    const { eventConfig } = dto;

    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );

    let systemConfig = await this.systemConfigRepo.findOneBy({
      key: SystemConfigKey.EVENT_CONFIG,
      ownerId: rootMerchantUserId,
    });
    if (!systemConfig) {
      systemConfig = this.systemConfigRepo.create({
        ownerId: rootMerchantUserId,
        key: SystemConfigKey.EVENT_CONFIG,
      });
    }

    systemConfig.value = eventConfig;

    await this.systemConfigRepo.save(systemConfig);

    return EventConfigResDto.forMerchant({ eventConfig });
  }

  async seed(dto: SeedEventConfigMerchantReqDto, userId: number) {
    const { eventConfig } = dto;

    const systemConfig = this.systemConfigRepo.create({
      key: SystemConfigKey.EVENT_CONFIG,
      ownerId: userId,
    });

    systemConfig.value = eventConfig;

    await this.systemConfigRepo.save(systemConfig);
  }
}
