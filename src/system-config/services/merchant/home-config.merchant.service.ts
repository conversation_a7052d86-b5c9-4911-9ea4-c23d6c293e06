import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { runOnTransactionCommit, Transactional } from 'typeorm-transactional';
import { User } from '../../../auth/entities/user.entity';
import { MerchantRepository } from '../../../auth/repositories/merchant.repository';
import { ReCacheIntDto } from '../../../cache/dtos/internal/cache.int.dto';
import { CacheKey } from '../../../cache/enums/cache.enum';
import { CacheService } from '../../../cache/services/cache.service';
import { CategoryMerchantService } from '../../../category/services/merchant/category.merchant.service';
import { EventName } from '../../../common/enums/event.enum';
import { NotFoundExc } from '../../../common/exceptions/custom.exception';
import { ProductStatus } from '../../../product/enums/product.enum';
import { ProductMerchantService } from '../../../product/services/merchant/product.merchant.service';
import { UtilService } from '../../../utils/services/util.service';
import { HomeSectionHorizontalProductList1 } from '../../dtos/common/home-config.common.dto';
import { HomeConfigResDto } from '../../dtos/common/res/home-config.res.dto';
import {
  SeedHomeConfigMerchantReqDto,
  UpdateHomeConfigMerchantReqDto,
} from '../../dtos/merchant/req/home-config.merchant.req.dto';
import { HomeSectionType } from '../../enums/home-config.enum';
import { SystemConfigKey } from '../../enums/index.num';
import { SystemConfigRepository } from '../../repositories/system-config.repository';

@Injectable()
export class HomeConfigMerchantService {
  constructor(
    private systemConfigRepo: SystemConfigRepository,
    private cacheService: CacheService,
    private eventEmitter: EventEmitter2,
    private merchantRepo: MerchantRepository,
    private utilService: UtilService,
  ) {}

  @Transactional()
  async get(user: User) {
    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );

    const homeConfig = await this.systemConfigRepo.findOneByOrThrowNotFoundExc({
      key: SystemConfigKey.HOME_CONFIG,
      ownerId: rootMerchantUserId,
    });

    if (!homeConfig)
      throw new NotFoundExc({
        message: 'system-config.homeConfig.common.homeConfigNotFound',
      });

    const sections = [];

    for (const section of homeConfig.value) {
      if (
        ![
          HomeSectionType.HORIZONTAL_PRODUCT_LIST_1,
          HomeSectionType.HORIZONTAL_PRODUCT_LIST_2,
        ].includes(section.type)
      ) {
        sections.push(section);
        continue;
      }

      const horizontalProductList =
        section as HomeSectionHorizontalProductList1;

      try {
        const categoryMerchantService = await this.utilService.getService(
          CategoryMerchantService,
        );
        const categoryId = horizontalProductList.data.categoryId;
        const category = await categoryMerchantService.getById(
          user,
          categoryId,
        );

        if (category) {
          const productMerchantService = await this.utilService.getService(
            ProductMerchantService,
          );
          const { items } = await productMerchantService.getList(user, {
            categoryId: categoryId,
            limit: horizontalProductList.data.maxLength,
            page: 1,
            status: ProductStatus.ACTIVE,
            isValidDate: true,
          });

          horizontalProductList.data.products = items;
          sections.push(horizontalProductList);
        }
      } catch {
        console.log('Home config get product failed');
        horizontalProductList.data.products = [];
      }
    }

    return HomeConfigResDto.forMerchant({
      sections: sections,
    });
  }

  @Transactional()
  async update(dto: UpdateHomeConfigMerchantReqDto, user: User) {
    const { sections } = dto;

    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );
    const rootMerchantId = await this.merchantRepo.getRootMerchantId(
      user.merchant,
    );

    let homeConfig = await this.systemConfigRepo.findOneBy({
      key: SystemConfigKey.HOME_CONFIG,
      ownerId: rootMerchantUserId,
    });
    if (!homeConfig) {
      homeConfig = this.systemConfigRepo.create({
        key: SystemConfigKey.HOME_CONFIG,
        ownerId: rootMerchantUserId,
      });
    }

    homeConfig.value = sections;

    await this.systemConfigRepo.save(homeConfig);

    runOnTransactionCommit(() => {
      this.eventEmitter.emit(EventName.CACHE_STALED, {
        merchantId: rootMerchantId,
        key: CacheKey.HOME_CONFIG,
      } as ReCacheIntDto);
    });

    return this.get(user);
  }

  @Transactional()
  async refresh(user: User) {
    const rootMerchantId = await this.merchantRepo.getRootMerchantId(
      user.merchant,
    );

    runOnTransactionCommit(() => {
      this.eventEmitter.emitAsync(EventName.CACHE_STALED, {
        merchantId: rootMerchantId,
        key: CacheKey.HOME_CONFIG,
      } as ReCacheIntDto);
    });
  }

  @Transactional()
  async seed(dto: SeedHomeConfigMerchantReqDto, userId: number) {
    const { sections } = dto;

    const homeConfig = this.systemConfigRepo.create({
      key: SystemConfigKey.HOME_CONFIG,
      ownerId: userId,
    });

    homeConfig.value = sections;

    await this.systemConfigRepo.save(homeConfig);
  }
}
