import { Injectable } from '@nestjs/common';
import { MerchantStatus } from '../../../auth/enums/merchant.enum';
import { MerchantRepository } from '../../../auth/repositories/merchant.repository';
import { NotFoundExc } from '../../../common/exceptions/custom.exception';
import { EventConfigResDto } from '../../dtos/common/res/event-config.res.dto';
import { SystemConfigKey } from '../../enums/index.num';
import { SystemConfigRepository } from '../../repositories/system-config.repository';

@Injectable()
export class EventConfigCustomerService {
  constructor(
    private systemConfigRepo: SystemConfigRepository,
    private merchantRepo: MerchantRepository,
  ) {}

  async get(merchantId: number) {
    const merchant = await this.merchantRepo.findOneByOrThrowNotFoundExc({
      id: merchantId,
      status: MerchantStatus.APPROVED,
    });

    const systemConfig =
      await this.systemConfigRepo.findOneByOrThrowNotFoundExc({
        ownerId: merchant.userId,
        key: SystemConfigKey.EVENT_CONFIG,
      });

    if (!systemConfig)
      throw new NotFoundExc({
        message: 'system-config.eventConfig.eventConfigNotFound',
      });

    return EventConfigResDto.forCustomer({
      eventConfig: systemConfig.value,
    });
  }

  async getByKey(merchantId: number, eventConfigKey: string): Promise<boolean> {
    const merchant = await this.merchantRepo.findOneByOrThrowNotFoundExc({
      id: merchantId,
      status: MerchantStatus.APPROVED,
    });

    const systemConfig =
      await this.systemConfigRepo.findOneByOrThrowNotFoundExc({
        ownerId: merchant.userId,
        key: SystemConfigKey.EVENT_CONFIG,
      });

    if (!systemConfig)
      throw new NotFoundExc({
        message: 'system-config.eventConfig.eventConfigNotFound',
      });

    for (const key of Object.keys(systemConfig.value)) {
      if (key === eventConfigKey) {
        return systemConfig.value[key];
      }
    }

    return false;
  }
}
