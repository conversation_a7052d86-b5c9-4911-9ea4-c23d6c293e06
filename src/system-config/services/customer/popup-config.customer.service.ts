import { Injectable } from '@nestjs/common';
import * as dayjs from 'dayjs';
import { MerchantStatus } from '../../../auth/enums/merchant.enum';
import { MerchantRepository } from '../../../auth/repositories/merchant.repository';
import { AppResponseDto } from '../../../common/dtos/app-response.dto';
import { BadRequestExc } from '../../../common/exceptions/custom.exception';
import { PopupListItem } from '../../dtos/common/popup-config.common.dto';
import { PopupConfigItemResDto } from '../../dtos/common/res/popup-config-item.res.dto';
import { SystemConfigKey } from '../../enums/index.num';
import { SystemConfigRepository } from '../../repositories/system-config.repository';

@Injectable()
export class PopupConfigCustomerService {
  constructor(
    private merchantRepo: MerchantRepository,
    private systemConfigRepo: SystemConfigRepository,
  ) {}

  async getAvailable(merchantId: number) {
    const merchant = await this.merchantRepo.findOneByOrThrowNotFoundExc({
      id: merchantId,
      status: MerchantStatus.APPROVED,
    });

    const systemConfig =
      await this.systemConfigRepo.findOneByOrThrowNotFoundExc({
        ownerId: merchant.userId,
        key: SystemConfigKey.POPUP_CONFIG,
      });

    if (!systemConfig.value)
      throw new BadRequestExc({
        message: 'system-config.popupConfig.popupConfigItemNotFound',
      });

    const popupConfig = systemConfig.value.sort(
      (a: PopupListItem, b: PopupListItem) => {
        if (a.ordinal < b.ordinal) {
          return -1;
        }
        if (a.ordinal > b.ordinal) {
          return 1;
        }

        return 0;
      },
    );

    const popupConfigItems = await popupConfig.filter(
      (item: PopupListItem) =>
        item.status === true &&
        dayjs().isAfter(dayjs(item.startDate)) &&
        dayjs().isBefore(dayjs(item.endDate)),
    );

    const results = popupConfigItems?.map((item) =>
      PopupConfigItemResDto.forCustomer(item),
    );

    return new AppResponseDto({ data: results });
  }
}
