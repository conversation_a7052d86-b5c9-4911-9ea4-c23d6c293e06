import { Injectable } from '@nestjs/common';
import { MerchantStatus } from '../../../auth/enums/merchant.enum';
import { MerchantRepository } from '../../../auth/repositories/merchant.repository';
import { UtilService } from '../../../utils/services/util.service';
import { DEFAULT_SHARE_APP_CONFIG_DATA } from '../../data/share-app-config.data';
import { ShareAppConfigResDto } from '../../dtos/common/res/share-app-config.res.dto';
import { SystemConfigKey } from '../../enums/index.num';
import { SystemConfigRepository } from '../../repositories/system-config.repository';

@Injectable()
export class ShareAppConfigCustomerService {
  constructor(
    private systemConfigRepo: SystemConfigRepository,
    private merchantRepo: MerchantRepository,
    private utilService: UtilService,
  ) {}

  async get(merchantId: number) {
    const merchant = await this.merchantRepo.findOneByOrThrowNotFoundExc({
      id: merchantId,
      status: MerchantStatus.APPROVED,
    });

    let shareAppConfig = await this.systemConfigRepo.findOneBy({
      key: SystemConfigKey.SHARE_APP_CONFIG,
      ownerId: merchant.userId,
    });

    if (!shareAppConfig) {
      shareAppConfig = this.systemConfigRepo.create({
        key: SystemConfigKey.SHARE_APP_CONFIG,
        value: DEFAULT_SHARE_APP_CONFIG_DATA,
        ownerId: merchant.userId,
      });
      await this.systemConfigRepo.insert(shareAppConfig);
    }

    return ShareAppConfigResDto.forCustomer({
      sections: shareAppConfig.value,
    });
  }
}
