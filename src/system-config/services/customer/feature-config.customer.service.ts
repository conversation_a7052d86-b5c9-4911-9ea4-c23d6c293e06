import { Injectable } from '@nestjs/common';
import { MerchantStatus } from '../../../auth/enums/merchant.enum';
import { MerchantRepository } from '../../../auth/repositories/merchant.repository';
import { NotFoundExc } from '../../../common/exceptions/custom.exception';
import { FeatureConfigResDto } from '../../dtos/common/res/feature-config.res.dto';
import { SystemConfigKey } from '../../enums/index.num';
import { SystemConfigRepository } from '../../repositories/system-config.repository';

@Injectable()
export class FeatureConfigCustomerService {
  constructor(
    private systemConfigRepo: SystemConfigRepository,
    private merchantRepo: MerchantRepository,
  ) {}

  async get(merchantId: number) {
    const merchant = await this.merchantRepo.findOneByOrThrowNotFoundExc({
      id: merchantId,
      status: MerchantStatus.APPROVED,
    });

    const systemConfig =
      await this.systemConfigRepo.findOneByOrThrowNotFoundExc({
        ownerId: merchant.userId,
        key: SystemConfigKey.FEATURE_CONFIG,
      });

    if (!systemConfig)
      throw new NotFoundExc({
        message: 'system-config.featureConfig.featureConfigNotFound',
      });

    return FeatureConfigResDto.forCustomer({
      featureConfig: systemConfig.value,
    });
  }

  async getByKey(
    merchantId: number,
    featureConfigKey: string,
  ): Promise<boolean> {
    const merchant = await this.merchantRepo.findOneByOrThrowNotFoundExc({
      id: merchantId,
      status: MerchantStatus.APPROVED,
    });

    const systemConfig =
      await this.systemConfigRepo.findOneByOrThrowNotFoundExc({
        ownerId: merchant.userId,
        key: SystemConfigKey.FEATURE_CONFIG,
      });

    if (!systemConfig)
      throw new NotFoundExc({
        message: 'system-config.featureConfig.featureConfigNotFound',
      });

    for (const key of Object.keys(systemConfig.value)) {
      if (key === featureConfigKey) {
        return systemConfig.value[key];
      }
    }

    return false;
  }
}
