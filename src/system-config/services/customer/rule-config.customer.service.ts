import { Injectable } from '@nestjs/common';
import { MerchantStatus } from '../../../auth/enums/merchant.enum';
import { MerchantRepository } from '../../../auth/repositories/merchant.repository';
import { NotFoundExc } from '../../../common/exceptions/custom.exception';
import { RuleConfigResDto } from '../../dtos/common/res/rule-config.res.dto';
import { SystemConfigKey } from '../../enums/index.num';
import { SystemConfigRepository } from '../../repositories/system-config.repository';

@Injectable()
export class RuleConfigCustomerService {
  constructor(
    private systemConfigRepo: SystemConfigRepository,
    private merchantRepo: MerchantRepository,
  ) {}

  async get(merchantId: number) {
    const merchant = await this.merchantRepo.findOneByOrThrowNotFoundExc({
      id: merchantId,
      status: MerchantStatus.APPROVED,
    });

    const systemConfig =
      await this.systemConfigRepo.findOneByOrThrowNotFoundExc({
        ownerId: merchant.userId,
        key: SystemConfigKey.RULE_CONFIG,
      });

    if (!systemConfig)
      throw new NotFoundExc({
        message: 'system-config.ruleConfig.ruleConfigNotFound',
      });

    return RuleConfigResDto.forCustomer({
      ruleConfig: systemConfig.value,
    });
  }

  async getByKey(
    merchantId,
    featureConfigKey: string,
  ): Promise<number | string> {
    const merchant = await this.merchantRepo.findOneByOrThrowNotFoundExc({
      id: merchantId,
      status: MerchantStatus.APPROVED,
    });

    const systemConfig =
      await this.systemConfigRepo.findOneByOrThrowNotFoundExc({
        ownerId: merchant.userId,
        key: SystemConfigKey.RULE_CONFIG,
      });

    if (!systemConfig)
      throw new NotFoundExc({
        message: 'system-config.ruleConfig.ruleConfigNotFound',
      });

    for (const key of Object.keys(systemConfig.value)) {
      if (key === featureConfigKey) {
        return systemConfig.value[key];
      }
    }

    return null;
  }
}
