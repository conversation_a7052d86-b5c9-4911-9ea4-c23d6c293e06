import { Injectable } from '@nestjs/common';
import { MerchantStatus } from '../../../auth/enums/merchant.enum';
import { MerchantRepository } from '../../../auth/repositories/merchant.repository';
import { AppConfigResDto } from '../../dtos/common/res/app-config.res.dto';
import { GetMobileAppVersionCustomerReqDto } from '../../dtos/customer/app-config.customer.req.dto';
import { AppConfigRepository } from '../../repositories/app-config.repository';

@Injectable()
export class AppConfigCustomerService {
  constructor(
    private appConfigRepo: AppConfigRepository,
    private merchantRepo: MerchantRepository,
  ) {}

  async getMobileAppVersion(
    merchantId: number,
    dto: GetMobileAppVersionCustomerReqDto,
  ) {
    const { deviceType } = dto;
    const merchant = await this.merchantRepo.findOneByOrThrowNotFoundExc({
      id: merchantId,
      status: MerchantStatus.APPROVED,
    });
    const appConfigs = await this.appConfigRepo.find({
      where: {
        ownerId: merchantId,
        ...(deviceType && { deviceType }),
      },
    });

    const result = appConfigs.map((item) => {
      return AppConfigResDto.forMerchant(item);
    });

    return result;
  }
}
