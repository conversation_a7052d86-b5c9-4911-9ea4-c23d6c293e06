import { Injectable } from '@nestjs/common';
import { MerchantStatus } from '../../../auth/enums/merchant.enum';
import { MerchantRepository } from '../../../auth/repositories/merchant.repository';
import { CategoryCustomertService } from '../../../category/services/customer/category.customer.service';
import { AppResponseDto } from '../../../common/dtos/app-response.dto';
import { ExpectationFailedExc } from '../../../common/exceptions/custom.exception';
import { ProductCustomerService } from '../../../product/services/customer/product.customer.service';
import { UtilService } from '../../../utils/services/util.service';
import { DEFAULT_HOME_CONFIG } from '../../data/index.data';
import { HomeSectionHorizontalProductList1 } from '../../dtos/common/home-config.common.dto';
import { HomeConfigResDto } from '../../dtos/common/res/home-config.res.dto';
import { HomeSectionType } from '../../enums/home-config.enum';
import { SystemConfigKey } from '../../enums/index.num';
import { SystemConfigRepository } from '../../repositories/system-config.repository';

@Injectable()
export class HomeConfigCustomerService {
  constructor(
    private systemConfigRepo: SystemConfigRepository,
    private merchantRepo: MerchantRepository,
    private utilService: UtilService,
  ) {}

  async get(merchantId: number) {
    const merchant = await this.merchantRepo.findOneByOrThrowNotFoundExc({
      id: merchantId,
      status: MerchantStatus.APPROVED,
    });

    let homeConfig = await this.systemConfigRepo.findOneBy({
      key: SystemConfigKey.HOME_CONFIG,
      ownerId: merchant.userId,
    });

    if (!homeConfig) {
      homeConfig = this.systemConfigRepo.create({
        key: SystemConfigKey.HOME_CONFIG,
        value: DEFAULT_HOME_CONFIG,
        ownerId: merchant.userId,
      });
      await this.systemConfigRepo.insert(homeConfig);
    }

    if (!Array.isArray(homeConfig.value)) {
      throw new ExpectationFailedExc({ message: 'common' });
    }

    const sections = [];

    for (const section of homeConfig.value) {
      if (
        ![
          HomeSectionType.HORIZONTAL_PRODUCT_LIST_1,
          HomeSectionType.HORIZONTAL_PRODUCT_LIST_2,
        ].includes(section.type)
      ) {
        sections.push(section);
        continue;
      }

      const horizontalProductList =
        section as HomeSectionHorizontalProductList1;

      try {
        const categoryCustomerService = await this.utilService.getService(
          CategoryCustomertService,
        );
        const categoryId = horizontalProductList.data.categoryId;
        const category = await categoryCustomerService.getById(
          merchantId,
          categoryId,
        );

        if (category) {
          const productCustomerService = await this.utilService.getService(
            ProductCustomerService,
          );
          const data = await productCustomerService.getList(merchantId, {
            categoryId: categoryId,
            limit: horizontalProductList.data.maxLength,
            page: 1,
          });

          horizontalProductList.data.products = data.response;
          sections.push(horizontalProductList);
        }
      } catch {
        console.log('Home config get product failed');
      }
    }

    return new AppResponseDto(
      HomeConfigResDto.forCustomer({
        sections: sections,
      }),
    );
  }
}
