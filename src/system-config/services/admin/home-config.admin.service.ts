import { Injectable } from '@nestjs/common';
import { MerchantResDto } from '../../../auth/dtos/common/res/merchant.res.dto';
import { NotFoundExc } from '../../../common/exceptions/custom.exception';
import { GetListHomeConfigAdminReqDto } from '../../dtos/admin/req/home-config.admin.req.dto';
import { HomeConfigResDto } from '../../dtos/common/res/home-config.res.dto';
import { SystemConfigKey } from '../../enums/index.num';
import { SystemConfigRepository } from '../../repositories/system-config.repository';

@Injectable()
export class HomeConfigAdminService {
  constructor(private systemConfigRepo: SystemConfigRepository) {}

  async get(dto: GetListHomeConfigAdminReqDto) {
    const homeConfigs = await this.systemConfigRepo.find({
      where: { key: SystemConfigKey.HOME_CONFIG },
      relations: { owner: { merchant: true } },
    });

    if (!homeConfigs)
      throw new NotFoundExc({
        message: 'system-config.homeConfig.common.homeConfigNotFound',
      });

    const result = homeConfigs.map((homeConfig) =>
      HomeConfigResDto.forAdmin({
        sections: homeConfig.value,
        merchant: MerchantResDto.forAdmin({ data: homeConfig.owner.merchant }),
      }),
    );

    return result;
  }
}
