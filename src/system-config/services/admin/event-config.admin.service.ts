import { Injectable } from '@nestjs/common';
import { paginate } from 'nestjs-typeorm-paginate';
import { NotFoundExc } from '../../../common/exceptions/custom.exception';
import { GetListEventConfigAdminReqDto } from '../../dtos/admin/req/event-config.admin.req.dto';
import { EventConfigResDto } from '../../dtos/common/res/event-config.res.dto';
import { SystemConfigKey } from '../../enums/index.num';
import { SystemConfigRepository } from '../../repositories/system-config.repository';

@Injectable()
export class EventConfigAdminService {
  constructor(private systemConfigRepo: SystemConfigRepository) {}

  async get(dto: GetListEventConfigAdminReqDto) {
    const { limit, page } = dto;

    const qb = this.systemConfigRepo
      .createQueryBuilder('sc')
      .where('sc.key = :key', { key: SystemConfigKey.EVENT_CONFIG })
      .innerJoinAndSelect('sc.owner', 'owner')
      .innerJoinAndSelect('owner.merchant', 'merchant');

    if (!qb)
      throw new NotFoundExc({
        message: 'system-config.eventConfig.eventConfigNotFound',
      });

    const { items, meta } = await paginate(qb, { limit, page });

    return items.map((item) => {
      item.owner.merchant.user = item.owner;

      return EventConfigResDto.forAdmin({
        eventConfig: item.value,
        merchant: item.owner.merchant,
      });
    });
  }
}
