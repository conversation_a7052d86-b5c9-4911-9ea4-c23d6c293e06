import { Injectable } from '@nestjs/common';
import { paginate } from 'nestjs-typeorm-paginate';
import { User } from '../../../auth/entities/user.entity';
import { NotFoundExc } from '../../../common/exceptions/custom.exception';
import {
  GetListFeatureConfigAdminReqDto,
  PostConfigAdminReqDto,
} from '../../dtos/admin/req/feature-config.admin.req.dto';
import { FeatureConfigResDto } from '../../dtos/common/res/feature-config.res.dto';
import { SystemConfigKey } from '../../enums/index.num';
import { SystemConfigRepository } from '../../repositories/system-config.repository';

@Injectable()
export class FeatureConfigAdminService {
  constructor(private systemConfigRepo: SystemConfigRepository) {}

  async get(dto: GetListFeatureConfigAdminReqDto) {
    const { limit, page } = dto;

    const qb = this.systemConfigRepo
      .createQueryBuilder('sc')
      .where('sc.key = :key', { key: SystemConfigKey.FEATURE_CONFIG })
      .innerJoinAndSelect('sc.owner', 'owner')
      .innerJoinAndSelect('owner.merchant', 'merchant');

    if (!qb)
      throw new NotFoundExc({
        message: 'system-config.featureConfig.featureConfigNotFound',
      });

    const { items, meta } = await paginate(qb, { limit, page });

    return items.map((item) => {
      item.owner.merchant.user = item.owner;

      return FeatureConfigResDto.forAdmin({
        featureConfig: item.value,
        merchant: item.owner.merchant,
      });
    });
  }

  async createConfig(body: PostConfigAdminReqDto, user: User) {
    const { key, value } = body;
    return this.systemConfigRepo.save({
      key,
      value,
      user,
    });
  }
}
