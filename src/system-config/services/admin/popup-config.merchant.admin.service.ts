import { Injectable } from '@nestjs/common';
import { paginate } from 'nestjs-typeorm-paginate';
import { MerchantRepository } from '../../../auth/repositories/merchant.repository';
import { NotFoundExc } from '../../../common/exceptions/custom.exception';
import { GetListPopupConfigAdminReqDto } from '../../dtos/admin/req/popup-config.admin.dto';
import { PopupConfigResDto } from '../../dtos/common/res/popup-config.res.dto';
import { SystemConfigKey } from '../../enums/index.num';
import { SystemConfigRepository } from '../../repositories/system-config.repository';

@Injectable()
export class PopupConfigAdminService {
  constructor(
    private merchantRepo: MerchantRepository,
    private systemConfigRepo: SystemConfigRepository,
  ) {}

  async get(dto: GetListPopupConfigAdminReqDto) {
    const { limit, page } = dto;

    const qb = this.systemConfigRepo
      .createQueryBuilder('sc')
      .where('sc.key = :key', { key: SystemConfigKey.POPUP_CONFIG })
      .innerJoinAndSelect('sc.owner', 'owner')
      .innerJoinAndSelect('owner.merchant', 'merchant');

    if (!qb)
      throw new NotFoundExc({
        message: 'system-config.popupConfig.popupConfigNotFound',
      });

    const { items, meta } = await paginate(qb, { limit, page });

    return items.map((item) => {
      item.owner.merchant.user = item.owner;

      return PopupConfigResDto.forAdmin({
        popupConfig: item.value,
        merchant: item.owner.merchant,
      });
    });
  }
}
