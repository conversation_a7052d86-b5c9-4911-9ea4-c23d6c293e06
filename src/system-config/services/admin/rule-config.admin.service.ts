import { Injectable } from '@nestjs/common';
import { paginate } from 'nestjs-typeorm-paginate';
import { NotFoundExc } from '../../../common/exceptions/custom.exception';
import { GetListRuleConfigAdminReqDto } from '../../dtos/admin/req/rule-config.admin.req.dto';
import { RuleConfigResDto } from '../../dtos/common/res/rule-config.res.dto';
import { SystemConfigKey } from '../../enums/index.num';
import { SystemConfigRepository } from '../../repositories/system-config.repository';

@Injectable()
export class RuleConfigAdminService {
  constructor(private systemConfigRepo: SystemConfigRepository) {}

  async get(dto: GetListRuleConfigAdminReqDto) {
    const { limit, page } = dto;

    const qb = this.systemConfigRepo
      .createQueryBuilder('sc')
      .where('sc.key = :key', { key: SystemConfigKey.RULE_CONFIG })
      .innerJoinAndSelect('sc.owner', 'owner')
      .innerJoinAndSelect('owner.merchant', 'merchant');

    if (!qb)
      throw new NotFoundExc({
        message: 'system-config.ruleConfig.ruleConfigNotFound',
      });

    const { items, meta } = await paginate(qb, { limit, page });

    return items.map((item) => {
      item.owner.merchant.user = item.owner;

      return RuleConfigResDto.forAdmin({
        ruleConfig: item.value,
        merchant: item.owner.merchant,
      });
    });
  }
}
