import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { EventName } from 'src/common/enums/event.enum';
import { DEFAULT_EVENT_CONFIG_DATA } from 'src/system-config/data/event-config.data';
import { DEFAULT_FEATURE_CONFIG_DATA } from 'src/system-config/data/feature-config.data';
import { DEFAULT_HOME_CONFIG_DATA } from 'src/system-config/data/home-config.data';
import { DEFAULT_RULE_CONFIG_DATA } from 'src/system-config/data/rule-config.data';
import { EventConfigMerchantService } from 'src/system-config/services/merchant/event-config.merchant.service';
import { FeatureConfigMerchantService } from 'src/system-config/services/merchant/feature-config.merchant.service';
import { HomeConfigMerchantService } from 'src/system-config/services/merchant/home-config.merchant.service';
import { RuleConfigMerchantService } from 'src/system-config/services/merchant/rule-config.merchant.service';
import { DEFAULT_POPUP_CONFIG_DATA } from '../../data/popup-config.data';
import { DEFAULT_SHARE_APP_CONFIG_DATA } from '../../data/share-app-config.data';
import { PopupConfigMerchantService } from '../../services/merchant/popup-config.merchant.service';
import { ShareAppConfigMerchantService } from '../../services/merchant/share-app-config.merchant.service';

@Injectable()
export class SystemConfigListener {
  constructor(
    private ruleConfigMerchantService: RuleConfigMerchantService,
    private homeConfigMerchantService: HomeConfigMerchantService,
    private featureConfigMerchantService: FeatureConfigMerchantService,
    private eventConfigMerchantService: EventConfigMerchantService,
    private popupConfigMerchantService: PopupConfigMerchantService,
    private shareAppConfigMerchantService: ShareAppConfigMerchantService,
  ) {}

  @OnEvent(EventName.MERCHANT_REGISTERED)
  async handleMerchantRegisteredEvent(userId: number) {
    await this.ruleConfigMerchantService.seed(DEFAULT_RULE_CONFIG_DATA, userId);
    await this.featureConfigMerchantService.seed(
      DEFAULT_FEATURE_CONFIG_DATA,
      userId,
    );
    await this.eventConfigMerchantService.seed(
      DEFAULT_EVENT_CONFIG_DATA,
      userId,
    );
    await this.homeConfigMerchantService.seed(DEFAULT_HOME_CONFIG_DATA, userId);
    await this.popupConfigMerchantService.seed(
      DEFAULT_POPUP_CONFIG_DATA,
      userId,
    );
    await this.shareAppConfigMerchantService.seed(
      DEFAULT_SHARE_APP_CONFIG_DATA,
      userId,
    );
  }
}
