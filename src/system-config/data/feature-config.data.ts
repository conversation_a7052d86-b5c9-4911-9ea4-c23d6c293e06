import { SeedFeatureConfigMerchantReqDto } from '../dtos/merchant/req/feature-config.merchant.req.dto';

export const DEFAULT_FEATURE_CONFIG_DATA: SeedFeatureConfigMerchantReqDto = {
  featureConfig: {
    UPGRADE_RANK: {
      desc: 'Chức năng lên hạng thành viên',
      status: false,
    },
    GIFT_EXCHANGE: {
      desc: 'Chức năng đổi quà',
      status: false,
    },
    VOUCHER_USING: {
      desc: 'Chức năng sử dụng voucer',
      status: false,
    },
    ADD_POINT: {
      desc: 'Chức năng quét mã',
      status: false,
    },
    USER_REGISTER: {
      desc: 'Chức năng đăng ký thành viên mới',
      status: false,
    },
    ADMIN_NOTIFICATION: {
      desc: 'Chức năng cho gửi thông báo từ admin',
      status: false,
    },
    USER_SURVEY: {
      desc: 'Chức năng cho khảo sát',
      status: false,
    },
    APP_SHARING: {
      desc: 'Chức năng chia sẻ ứng dụng',
      status: false,
    },
    APP_UPDATE: {
      desc: 'Chức năng yêu cầu cập nhập',
      status: false,
    },
    ZALO_OA: {
      desc: 'Chức năng gửi tin nhắn qua Zalo OA',
      status: false,
    },
    USER_FEED_BACK: {
      desc: 'Chức năng góp ý',
      status: true,
    },
  },
};
