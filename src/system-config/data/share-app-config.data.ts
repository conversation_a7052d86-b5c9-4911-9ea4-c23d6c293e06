import { MobileRouteType } from '../../common/enums/app.enum';
import { SeedShareAppConfigMerchantReqDto } from '../dtos/merchant/req/share-app-config.merchant.req.dto';

export const DEFAULT_SHARE_APP_CONFIG_DATA: SeedShareAppConfigMerchantReqDto = {
  sections: [
    {
      data: [
        {
          link: '',
          image:
            'https://awd-dev-bucket.s3.ap-southeast-1.amazonaws.com/images/14/ca2bc379-e80d-4b49-bde0-41b1be0ae8c9.png',
          params: {},
          imageId: 2118,
          typeRoute: MobileRouteType.DEEP_LINK,
        },
        {
          link: '',
          image:
            'https://awd-dev-bucket.s3.ap-southeast-1.amazonaws.com/images/14/ca2bc379-e80d-4b49-bde0-41b1be0ae8c9.png',
          params: {},
          imageId: 2118,
          typeRoute: MobileRouteType.DEEP_LINK,
        },
      ],
    },
    {
      data: [
        {
          link: '',
          image:
            'https://awd-dev-bucket.s3.ap-southeast-1.amazonaws.com/images/14/ca2bc379-e80d-4b49-bde0-41b1be0ae8c9.png',
          params: {},
          imageId: 2118,
          typeRoute: MobileRouteType.DEEP_LINK,
        },
        {
          link: '',
          image:
            'https://awd-dev-bucket.s3.ap-southeast-1.amazonaws.com/images/14/ca2bc379-e80d-4b49-bde0-41b1be0ae8c9.png',
          params: {},
          imageId: 2118,
          typeRoute: MobileRouteType.DEEP_LINK,
        },
      ],
    },
  ],
};
