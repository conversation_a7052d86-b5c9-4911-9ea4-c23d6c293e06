import { SeedRuleConfigMerchantReqDto } from '../dtos/merchant/req/rule-config.merchant.req.dto';

export const DEFAULT_RULE_CONFIG_DATA: SeedRuleConfigMerchantReqDto = {
  ruleConfig: {
    GAME_LUCKY_WHEEL_POINT: {
      desc: 'Quy định điểm khi scan QR',
      status: false,
      values: {
        MEMBER_POINT: 0,
        TITAN_POINT: 0,
        GOLD_POINT: 0,
        PLATINUM_POINT: 0,
      },
    },
    BIRTHDAY_NOTIFICATION_POINT: {
      desc: 'Quy định điểm về sự kiện sinh nhật',
      status: false,
      values: {
        MEMBER_POINT: 0,
        TITAN_POINT: 0,
        GOLD_POINT: 0,
        PLATINUM_POINT: 0,
      },
    },
    FIRST_TIME_ADD_POINT: {
      desc: 'Quy định điểm khi tài khoản quét mã lần đầu',
      status: false,
      values: {
        DEFAULT_POINT: 0,
      },
    },
    REFERRAL_APP_POINT: {
      desc: 'Quy định điểm khi người dùng giới thiệu ứng dụng cho người dùng mới',
      status: false,
      values: {
        DEFAULT_POINT: 0,
      },
    },
    LIMITED_ADD_POINT_TURN: {
      desc: 'Quy định mức giới hạn cho phép người dùng có thể quét mã',
      status: false,
      values: {
        LIMITED_TURN: 1,
      },
    },
    BLOCK_USER_WRONG_RULE_TURN: {
      desc: 'Quy định về khóa tài khoản theo số lần vi phạm của người dùng',
      status: true,
      values: {
        LIMITED_TURN: 1,
      },
    },
    BLOCK_ADD_POINT_WRONG_RULE_TURN: {
      desc: 'Quy định về khóa chức năng tích điểm theo số lần vi phạm của người dùng',
      status: true,
      values: {
        LIMITED_TURN: 1,
      },
    },
    RESET_POINT_TIME: {
      desc: 'Quy định thời gian reset điểm',
      status: true,
      values: {
        RULE_DATE: '3d',
      },
    },
    RESET_RANK_TIME: {
      desc: 'Quy đinh thời gian reset thứ hạng thành viên',
      status: true,
      values: {
        RULE_DATE: '3d',
      },
    },
  },
};
