import { Module } from '@nestjs/common';
import { TypeOrmCustomModule } from 'utility/dist';
import { MerchantRepository } from '../auth/repositories/merchant.repository';
import { CacheModule } from '../cache/cache.module';
import { UtilsModule } from '../utils/utils.module';
import { HomeConfigCustomerController } from './controllers/customer/home-config.customer.controller';
import { PopupConfigCustomerController } from './controllers/customer/popup-config.customer.controller';
import { HomeConfigMerchantController } from './controllers/merchant/home-config.merchant.controller';
import { PopupConfigMerchantController } from './controllers/merchant/popup-config.merchant.controller';
import { AppConfigRepository } from './repositories/app-config.repository';
import { SecretRepository } from './repositories/secret.repository';
import { SystemConfigRepository } from './repositories/system-config.repository';
import { HomeConfigCustomerService } from './services/customer/home-config.customer.service';
import { PopupConfigCustomerService } from './services/customer/popup-config.customer.service';
import { HomeConfigMerchantService } from './services/merchant/home-config.merchant.service';
import { PopupConfigMerchantService } from './services/merchant/popup-config.merchant.service';

@Module({
  imports: [
    TypeOrmCustomModule.forFeature([
      SystemConfigRepository,
      MerchantRepository,
      SecretRepository,
      AppConfigRepository,
    ]),
    CacheModule,
    UtilsModule,
  ],
  controllers: [
    HomeConfigMerchantController,
    HomeConfigCustomerController,
    PopupConfigMerchantController,
    PopupConfigCustomerController,
    // HomeConfigAdminController,
    // FeatureConfigAdminController,
    // FeatureConfigMerchantController,
    // FeatureConfigCustomerController,
    // RuleConfigMerchantController,
    // RuleConfigCustomerController,
    // RuleConfigAdminController,
    // SecretMerchantController,
    // EventConfigMerchantController,
    // EventConfigCustomerController,
    // EventConfigAdminController,
    // PopupConfigAdminController,
    // AppConfigMerchantController,
    // AppConfigCustomerController,
    // ShareAppConfigMerchantController,
    // ShareAppConfigCustomerController,
  ],
  providers: [
    HomeConfigMerchantService,
    HomeConfigCustomerService,
    PopupConfigMerchantService,
    PopupConfigCustomerService,
    // PopupConfigAdminService,
    // HomeConfigAdminService,
    // FeatureConfigAdminService,
    // FeatureConfigCustomerService,
    // FeatureConfigMerchantService,
    // FeatureConfigService,
    // RuleConfigMerchantService,
    // RuleConfigCustomerService,
    // RuleConfigAdminService,
    // RuleConfigService,
    // SecretMerchantService,
    // EventConfigMerchantService,
    // EventConfigCustomerService,
    // EventConfigAdminService,
    // AppConfigMerchantService,
    // AppConfigCustomerService,
    // ShareAppConfigMerchantService,
    // ShareAppConfigCustomerService,
  ],
  // exports: [FeatureConfigService, RuleConfigService],
})
export class SystemConfigModule {}
