import {
  registerDecorator,
  ValidationArguments,
  ValidationOptions,
} from 'class-validator';
import { getValEnumStr } from '../../common/helpers/enum.helper';
import { FeatureConfig } from '../dtos/common/feature-config.common.dto';
import { FeatureConfigType } from '../enums/feature-config.enum';

export function IsValidFeatureConfig(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'IsValidFeatureConfig',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [],
      options: validationOptions,
      async: true,
      validator: {
        async validate(value: FeatureConfig, args: ValidationArguments) {
          if (typeof value !== 'object') return false;

          const types =
            getValEnumStr<keyof typeof FeatureConfigType>(FeatureConfigType);

          if (Object.keys(value).length !== types.length) return false;

          for (const key of Object.keys(value)) {
            //check key
            if (!types.includes(key as any) || typeof value[key] !== 'object') {
              return false;
            }

            //check properties of key
            if (
              typeof value[key].desc !== 'string' ||
              typeof value[key].status !== 'boolean'
            ) {
              return false;
            }
          }

          return true;
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} with value ${args?.value} is invalid FeatureConfig, some featureType is missing, or value is not boolean type!`;
        },
      },
    });
  };
}
