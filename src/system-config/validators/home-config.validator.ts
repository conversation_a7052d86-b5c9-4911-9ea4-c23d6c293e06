import { plainToInstance } from 'class-transformer';
import {
  registerDecorator,
  validateOrReject,
  ValidationArguments,
  ValidationOptions,
} from 'class-validator';
import {
  HomeSection,
  HomeSectionBanner,
  HomeSectionHorizontalProductList1,
  HomeSectionHorizontalProductList2,
  HomeSectionNormalService,
} from '../dtos/common/home-config.common.dto';
import { HomeSectionType } from '../enums/home-config.enum';

export function IsValidHomeConfig(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'IsValidHomeConfig',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [],
      options: validationOptions,
      async: true,
      validator: {
        async validate(value: HomeSection[], args: ValidationArguments) {
          if (!value?.length) return false;

          try {
            await Promise.all(
              value.map(async (homeSection) => {
                switch (homeSection.type) {
                  case HomeSectionType.BANNER:
                    return validateOrReject(
                      plainToInstance(HomeSectionBanner, homeSection),
                    );
                  case HomeSectionType.NORMAL_SERVICE:
                    return validateOrReject(
                      plainToInstance(HomeSectionNormalService, homeSection),
                    );
                  case HomeSectionType.HORIZONTAL_PRODUCT_LIST_1:
                    return validateOrReject(
                      plainToInstance(
                        HomeSectionHorizontalProductList1,
                        homeSection,
                      ),
                    );
                  case HomeSectionType.HORIZONTAL_PRODUCT_LIST_2:
                    return validateOrReject(
                      plainToInstance(
                        HomeSectionHorizontalProductList2,
                        homeSection,
                      ),
                    );
                  default:
                    throw new Error('Home section type is invalid');
                }
              }),
            );
          } catch (error) {
            console.log('error', error);
            return false;
          }

          return true;
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} with value ${args?.value} is invalid HomeConfig`;
        },
      },
    });
  };
}
