import {
  registerDecorator,
  ValidationArguments,
  ValidationOptions,
} from 'class-validator';
import { getValEnumStr } from '../../common/helpers/enum.helper';
import { EventConfig } from '../dtos/common/event-config.common.dto';
import { EventConfigType } from '../enums/event-config.enum';

export function IsValidEventConfig(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'IsValidEventConfig',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [],
      options: validationOptions,
      async: true,
      validator: {
        async validate(value: EventConfig, args: ValidationArguments) {
          if (typeof value !== 'object') return false;

          const types =
            getValEnumStr<keyof typeof EventConfigType>(EventConfigType);

          if (Object.keys(value).length !== types.length) return false;

          return true;
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} with value ${args?.value} is invalid FeatureConfig`;
        },
      },
    });
  };
}
