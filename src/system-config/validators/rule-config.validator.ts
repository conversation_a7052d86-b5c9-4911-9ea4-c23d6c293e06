import {
  registerDecorator,
  ValidationArguments,
  ValidationOptions,
} from 'class-validator';
import { getValEnumStr } from '../../common/helpers/enum.helper';
import { RuleConfig, RuleValue } from '../dtos/common/rule-config.common.dto';
import { RuleConfigType } from '../enums/rule-config.enum';

export function IsValidRuleConfig(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'IsValidRuleConfig',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [],
      options: validationOptions,
      async: true,
      validator: {
        async validate(value: RuleConfig, args: ValidationArguments) {
          if (typeof value !== 'object') return false;

          const types =
            getValEnumStr<keyof typeof RuleConfigType>(RuleConfigType);
          if (Object.keys(value).length !== types.length) return false;

          for (const key of Object.keys(value)) {
            const ruleValues = value[key].values;
            //check key
            if (
              !types.includes(key as any) ||
              value[key] instanceof RuleValue
            ) {
              return false;
            }
          }

          return true;
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} with value ${args?.value} is invalid RuleConfig`;
        },
      },
    });
  };
}
