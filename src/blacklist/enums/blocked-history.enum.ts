export enum BlockedHistoryType {
  ACCOUNT = 'ACCOUNT',
  SCAN_QR_CODE = 'SCAN_QR_CODE',
}

export enum BlockedHistoryAction {
  BLOCK = 'BLOCK',
  UN_BLOCK = 'UN_BLOCK',
}

export enum BlockedReason {
  ADMIN_BLOCK_SCAN_QR_CODE = 'admin block scan qr code',
  ADMIN_UN_BLOCK_SCAN_QR_CODE = 'admin unblock scan qr code',
  ADMIN_BLOCK_ACCOUNT = 'admin block account',
  ADMIN_UN_BLOCK_ACCOUNT = 'admin unblock account',
  BLOCK_SCAN_QR_CODE_SCAN_FAILED = 'scan failed many times',
  BLOCK_ACCOUNT_SCAN_FAILED = 'blocked many times',
}
