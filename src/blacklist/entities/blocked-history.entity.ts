import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  ManyTo<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { User } from '../../auth/entities/user.entity';
import { BaseEntity } from '../../common/entities/base.entity';
import {
  BlockedHistoryAction,
  BlockedHistoryType,
} from '../enums/blocked-history.enum';

@Entity('blocked_history')
export class BlockedHistory extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'character varying' })
  type: BlockedHistoryType;

  @Column({ type: 'text' })
  reason: string;

  @Column({ nullable: true })
  description: string;

  @Column({ name: 'expire_date', type: 'timestamptz', nullable: true })
  expireDate: Date;

  @CreateDateColumn({ name: 'action_date', type: 'timestamptz' })
  actionDate: Date;

  @Column({ type: 'enum', enum: BlockedHistoryAction, nullable: true })
  action: BlockedHistoryAction;

  // Join user
  @Column({ name: 'user_id' })
  userId: number;

  @ManyToOne(() => User, (user) => user.blockedHistories)
  @JoinColumn()
  user: User;
  // End join user
}
