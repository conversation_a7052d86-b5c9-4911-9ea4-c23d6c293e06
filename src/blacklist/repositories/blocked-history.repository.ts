import { Injectable } from '@nestjs/common';
import { Brackets, DataSource } from 'typeorm';
import { BlockStatus } from '../../auth/enums/customer.enum';
import { BaseRepository } from '../../common/repositories/base.repositories';
import { I18nPath } from '../../i18n/i18n.generated';
import { BlockedHistory } from '../entities/blocked-history.entity';
import {
  BlockedHistoryAction,
  BlockedHistoryType,
} from '../enums/blocked-history.enum';

@Injectable()
export class BlockedHistoryRepository extends BaseRepository<BlockedHistory> {
  entityNameI18nKey: I18nPath;
  constructor(dataSource: DataSource) {
    super(BlockedHistory, dataSource);
    this.entityNameI18nKey = 'common.word.blockedHistory';
  }

  async getBlockStatus(userId: number): Promise<BlockStatus> {
    const blockedAccount = await this.createQueryBuilder('blockedHistoryRepo')
      .where('blockedHistoryRepo.userId = :userId', { userId })
      .where('blockedHistoryRepo.type = :type', {
        type: BlockedHistoryType.ACCOUNT,
      })
      .andWhere(
        new Brackets((qb2) => {
          qb2
            .where('blockedHistoryRepo.expireDate is null')
            .orWhere('blockedHistoryRepo.expireDate > now()');
        }),
      )
      .orderBy('blockedHistoryRepo.createdAt', 'DESC')
      .getOne();

    if (blockedAccount && blockedAccount.action === BlockedHistoryAction.BLOCK)
      return BlockStatus.BLOCK_ACCOUNT;

    const blockedScanQrCode = await this.createQueryBuilder(
      'blockedHistoryRepo',
    )
      .where('blockedHistoryRepo.userId = :userId', { userId })
      .andWhere('blockedHistoryRepo.type = :type', {
        type: BlockedHistoryType.SCAN_QR_CODE,
      })
      .andWhere(
        new Brackets((qb2) => {
          qb2
            .where('blockedHistoryRepo.expireDate is null')
            .orWhere('blockedHistoryRepo.expireDate > now()');
        }),
      )
      .orderBy('blockedHistoryRepo.createdAt', 'DESC')
      .getOne();

    if (
      blockedScanQrCode &&
      blockedScanQrCode.action === BlockedHistoryAction.BLOCK
    )
      return BlockStatus.BLOCK_SCAN_QR_CODE;

    return BlockStatus.UN_BLOCK;
  }
}
