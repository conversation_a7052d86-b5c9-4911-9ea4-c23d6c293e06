import { RedisModule } from '@liaoliaots/nestjs-redis';
import { HttpModule } from '@nestjs/axios';
import { BullModule } from '@nestjs/bull';
import { forwardRef, Module, OnModuleInit } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ThrottlerModule } from '@nestjs/throttler';
import { TypeOrmModule } from '@nestjs/typeorm';
import * as dayjs from 'dayjs';
import * as customParseFormat from 'dayjs/plugin/customParseFormat';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';
import {
  AcceptLanguageResolver,
  HeaderResolver,
  I18nModule,
  QueryResolver,
} from 'nestjs-i18n';
import * as path from 'path';
import {
  addTransactionalDataSource,
  initializeTransactionalContext,
} from 'typeorm-transactional';
import { TypeOrmCustomModule } from 'utility/dist';
import { dataSource } from '../../data-source';
import { AuthModule } from '../auth/auth.module';
import { CustomerRepository } from '../auth/repositories/customer.repository';
import { MerchantRepository } from '../auth/repositories/merchant.repository';
import { UserRepository } from '../auth/repositories/user.repository';
import { CaslModule } from '../casl/casl.module';
import { bullConfig, bullQueues } from '../common/config/bull.config';
import globalConfig, { GlobalConfig } from '../common/config/global.config';
import { redisConfig } from '../common/config/redis.config';
import { TIME_ZONE } from '../common/constants/global.constant';
import { RequestExportRepository } from '../export/repositories/request-export.repositories';
import { FileRequestDownloadHistoryRepository } from '../file-request/repositories/file-request-download-history.repository';
import { FileRequestQrToGiftRepository } from '../file-request/repositories/file-request-qr-to-gift.repository';
import { FileRequestQrToPointRepository } from '../file-request/repositories/file-request-qr-to-point.repository';
import { FileRequestRepository } from '../file-request/repositories/file-request.repository';
import { FileRequestMerchantService } from '../file-request/services/merchant/file-request.merchant.service';
import { FileModule } from '../file/file.module';
import { FileService } from '../file/file.service';
import { FileRepository } from '../file/repositories/file.repository';
import { QrCodeModule } from '../qr-code/qr-code.module';
import { QrCodeRepository } from '../qr-code/repositories/qr-code.repository';
import { ScanHistoryRepository } from '../qr-code/repositories/scan-history.repository';
import { QrCodeMerchantService } from '../qr-code/services/merchant/qr-code.merchant.service';
import { SecretRepository } from '../system-config/repositories/secret.repository';
import { SystemConfigRepository } from '../system-config/repositories/system-config.repository';
import { UploadService } from '../utils/services/upload-file.service';
import { UuidService } from '../utils/services/uuid.service';
import { UtilsModule } from '../utils/utils.module';
import { ExportProcessor } from './processors/export.processor';
import { FileRequestProcessor } from './processors/file-request.processor';
import { ImportProcessor } from './processors/import.processor';

@Module({
  imports: [
    RedisModule.forRootAsync(redisConfig),
    BullModule.forRootAsync(bullConfig),
    BullModule.registerQueue(...bullQueues),
    EventEmitterModule.forRoot({
      maxListeners: 50,
    }),
    ConfigModule.forRoot({
      isGlobal: true,
      load: [() => globalConfig],
      cache: true,
    }),
    TypeOrmModule.forRootAsync({
      useFactory: () => ({}),
      dataSourceFactory: async () => {
        initializeTransactionalContext();
        return addTransactionalDataSource(dataSource);
      },
    }),
    TypeOrmCustomModule.forFeature([
      SecretRepository,
      CustomerRepository,
      UserRepository,
      MerchantRepository,
      FileRepository,
      SystemConfigRepository,
      FileRequestRepository,
      FileRequestQrToGiftRepository,
      FileRequestQrToPointRepository,
      QrCodeRepository,
      FileRequestDownloadHistoryRepository,
      RequestExportRepository,
      ScanHistoryRepository,
    ]),
    I18nModule.forRoot({
      fallbackLanguage: 'vi',
      loaderOptions: { path: path.join(__dirname, '..', 'i18n'), watch: true },
      resolvers: [
        new QueryResolver(['lang', 'l']),
        new HeaderResolver(['lang', 'l']),
        AcceptLanguageResolver,
      ],
      typesOutputPath: path.join(process.cwd(), '/src/i18n/i18n.generated.ts'),
    }),
    ThrottlerModule.forRoot([{ ttl: 60, limit: 10 }]),
    HttpModule,
    CaslModule,
    AuthModule,
    UtilsModule,
    QrCodeModule,
    forwardRef(() => FileModule),
  ],
  providers: [
    ExportProcessor,
    ImportProcessor,
    FileRequestProcessor,
    FileService,
    UploadService,
    UuidService,
    FileRequestMerchantService,
    QrCodeMerchantService,
  ],
})
export class WorkerModule implements OnModuleInit {
  constructor(private configService: ConfigService<GlobalConfig>) {}

  async onModuleInit() {
    await dataSource.query('CREATE extension IF NOT EXISTS pgcrypto');
    dayjs.extend(utc);
    dayjs.extend(timezone);
    dayjs.tz.setDefault(TIME_ZONE);
    dayjs.extend(customParseFormat);
  }
}
