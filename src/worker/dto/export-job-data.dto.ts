import { GetListCustomerMerchantReqDto } from '../../auth/dtos/merchant/req/customer.merchant.req.dto';
import { NonFunctionProperties } from '../../common/types/utils.type';

export class ExportJobDataDto<T = any> {
  requestExportId: number;

  constructor({ requestExportId }: NonFunctionProperties<ExportJobDataDto>) {
    this.requestExportId = requestExportId;
  }
}

export type IExportJobDataDto = ExportJobDataDto<GetListCustomerMerchantReqDto>;
