import {
  OnQueueActive,
  OnQueueCleaned,
  OnQueueCompleted,
  OnQueueDrained,
  OnQueueError,
  OnQueueFailed,
  OnQueuePaused,
  OnQueueProgress,
  OnQueueRemoved,
  OnQueueResumed,
  OnQueueStalled,
  OnQueueWaiting,
  Process,
  Processor,
} from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { FileRequestMerchantService } from '../../file-request/services/merchant/file-request.merchant.service';
import { QueueName } from '../../worker/enums/worker.enum';

@Processor(QueueName.GENERATE_QR_CODE)
export class FileRequestProcessor {
  private logger = new Logger(FileRequestProcessor.name);
  constructor(
    private readonly fileRequestMerchantService: FileRequestMerchantService,
  ) {}

  @OnQueueFailed()
  async queueFail(job: Job<any>) {
    console.log(`${QueueName.GENERATE_QR_CODE}_Fail: `, job.id);
  }

  @OnQueueWaiting()
  async queueWaiting(jobId: number) {
    console.log(`${QueueName.GENERATE_QR_CODE}_Waiting: `, jobId);
  }
  @OnQueuePaused()
  async queuePaused(job: Job<any>) {
    console.log(`${QueueName.GENERATE_QR_CODE}_Paused: `, job.id);
  }
  @OnQueueResumed()
  async queueResumed(job: Job<any>) {
    console.log(`${QueueName.GENERATE_QR_CODE}_Resumed: `, job.id);
  }
  @OnQueueActive()
  async queueActive(job: Job<any>) {
    console.log(`${QueueName.GENERATE_QR_CODE}_Active: `, job.id);
  }

  @OnQueueCompleted()
  async queueCompleted(job: Job<any>) {
    console.log(`${QueueName.GENERATE_QR_CODE}_Completed: `, job.id);
    await this.fileRequestMerchantService.updateStatusFileRequestAfterGenenareQrCode(
      job.data?.fileRequest,
    );
  }

  @OnQueueCleaned()
  async queueCleaned(job: Job<any>) {
    console.log(`${QueueName.GENERATE_QR_CODE}_Cleaned: `, job.id);
  }

  @OnQueueDrained()
  async queueDrained() {
    console.log(`${QueueName.GENERATE_QR_CODE}_Drained: `);
  }

  @OnQueueRemoved()
  async onQueueRemoved(job: Job<any>) {
    console.log(`${QueueName.GENERATE_QR_CODE}_Removed: `, job.id);
  }

  @OnQueueProgress()
  async onQueueProgress(job: Job<any>) {
    console.log(`${QueueName.GENERATE_QR_CODE}_Progress: `, job.id);
  }

  @OnQueueStalled()
  async onQueueStalled(job: Job<any>) {
    console.log(`${QueueName.GENERATE_QR_CODE}_Stalled: `, job.id);
  }

  @OnQueueError()
  async onQueueError(error: any) {
    console.log(`${QueueName.GENERATE_QR_CODE}_Error: `, error);
  }

  @Process()
  async readOperationJob(job: Job<any>) {
    console.log(`${QueueName.GENERATE_QR_CODE}_JobId: `, job.id);
    await this.fileRequestMerchantService.generateQrCode(
      job.data?.fileRequest,
      job.data?.qrData,
      job.data?.quantity,
    );
    job.progress(100);
    return {};
  }
}
