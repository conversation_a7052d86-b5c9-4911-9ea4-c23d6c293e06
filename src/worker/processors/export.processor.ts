import {
  OnQueueActive,
  OnQueueCompleted,
  OnQueueFailed,
  OnQueueStalled,
  Process,
  Processor,
} from '@nestjs/bull';

import { Logger } from '@nestjs/common';

import { Job } from 'bull';

import {
  RequestExportStatus,
  RequestExportType,
} from '../../export/enums/request-export.enum';

import { RequestExportRepository } from '../../export/repositories/request-export.repositories';

import { IExportJobDataDto } from '../dto/export-job-data.dto';

import { Resource } from '../../common/enums/casl.enum';

import { QrCodeMerchantService } from '../../qr-code/services/merchant/qr-code.merchant.service';

import { File } from '../../file/entities/file.entity';
import { QueueName } from '../enums/worker.enum';

@Processor(QueueName.EXPORT)
export class ExportProcessor {
  private logger = new Logger(ExportProcessor.name);

  constructor(
    private requestExportRepo: RequestExportRepository,
    private qrCodeMerchantService: QrCodeMerchantService,
  ) {}

  @Process({ concurrency: 5 })
  async handleExportJob(job: Job<IExportJobDataDto>) {
    this.logger.log(`Processing job ${job.id}`);
    const { requestExportId } = job.data;
    let file: File;

    const requestExport = await this.requestExportRepo.findOne({
      where: { id: requestExportId },
      relations: { owner: { merchant: true } },
    });

    if (!requestExport) {
      this.logger.error(
        `Request export not found. JobId=${job.id}, requestExportId=${requestExportId}`,
      );
      return;
    }
    if (requestExport.status === RequestExportStatus.COMPLETED) {
      this.logger.error(`RequestExportId=${requestExportId} completed.`);
      return;
    }

    const params = requestExport.params;

    try {
      await this.requestExportRepo.update(requestExportId, {
        status: RequestExportStatus.PROCESSING,
      });

      switch (requestExport.resource) {
        case Resource.QR_CODE:
          if (requestExport.type === RequestExportType.MERCHANT) {
            file = await this.qrCodeMerchantService.handleExport(
              params as any,
              requestExport.owner,
              requestExport.fileName,
            );
          }
        default:
          break;
      }
    } catch (error) {
      this.logger.error(`❌ Export job ${job.id} error!`, error);
      await this.requestExportRepo.update(job.data.requestExportId, {
        status: RequestExportStatus.FAILED,
      });
    }

    await this.requestExportRepo.update(requestExportId, {
      fileId: file.id,
      status: RequestExportStatus.COMPLETED,
    });
  }

  @OnQueueActive()
  async onActive(job: Job<IExportJobDataDto>) {
    try {
      this.logger.log(
        `job requestExportId=${job.data.requestExportId} is active`,
      );
    } catch (error) {}
  }

  @OnQueueCompleted()
  async onCompleted(job: Job<IExportJobDataDto>) {
    try {
      this.logger.log(`complete, jobData=${JSON.stringify(job)}`);
      this.logger.log(
        `job requestExportId=${job.data.requestExportId} is completed`,
      );
    } catch (error) {}
  }

  @OnQueueStalled()
  async onStalled(job: Job<IExportJobDataDto>) {
    try {
      const { requestExportId } = job.data;
      this.logger.warn(`job requestExportId=${requestExportId} is stalled`);
    } catch (error) {}
  }

  @OnQueueFailed()
  async onFailed(job: Job<IExportJobDataDto>, error: Error) {
    try {
      const { requestExportId } = job.data;
      this.logger.log(`job requestExportId=${requestExportId} is failed`);

      if (job.attemptsMade >= job.opts.attempts) {
        await this.requestExportRepo.update(requestExportId, {
          status: RequestExportStatus.FAILED,
        });
      }
    } catch (error) {}
  }
}
