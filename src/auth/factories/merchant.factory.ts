import { faker } from '@faker-js/faker';
import { Factory, SingleSubfactory } from '@jorgebodega/typeorm-factory';
import {
  Constructable,
  FactorizedAttrs,
} from '@jorgebodega/typeorm-factory/dist/types';
import { DataSource } from 'typeorm';
import { dataSource } from '../../../data-source';
import { Merchant } from '../entities/merchant.entity';
import { MerchantRank, MerchantStatus } from '../enums/merchant.enum';
import { UserType } from '../enums/user.enum';
import { UserFactory } from './user.factory';

export class MerchantFactory extends Factory<Merchant> {
  protected entity: Constructable<Merchant> = Merchant;
  protected dataSource: DataSource = dataSource;
  protected attrs(): FactorizedAttrs<Merchant> {
    return {
      name: faker.name.fullName(),
      email: faker.internet.email(),
      password: faker.internet.password(),
      status: MerchantStatus.APPROVED,
      rank: MerchantRank.BASIC,
      address: faker.address.streetAddress(true),
      phoneNumber: faker.phone.number(),
      user: new SingleSubfactory(UserFactory, { type: UserType.MERCHANT }),
    };
  }
}

export const merchantFactory = new MerchantFactory();
