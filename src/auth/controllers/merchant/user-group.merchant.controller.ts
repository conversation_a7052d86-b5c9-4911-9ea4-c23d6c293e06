import { Body, Controller, ParseIntPipe, Post, Put } from '@nestjs/common';
import { Delete, Get, Param, Query } from '@nestjs/common/decorators';
import { ApiTags } from '@nestjs/swagger';
import { Action, Resource } from 'src/common/enums/casl.enum';
import { PrefixType } from '../../../common/constants/global.constant';
import {
  AuthorizeMerchant,
  CurrentAuthData,
} from '../../../common/decorators/auth.decorator';
import { PaginationResponse } from '../../../common/decorators/swagger.decorator';
import { DeleteMultipleByIdNumberReqDto } from '../../../common/dtos/delete-multiple.dto';

import { UserGroupResDto } from '../../dtos/common/res/user-group.res.dto';
import {
  CreateGroupAllCustomerMerchantReqDto,
  CreateGroupCustomerMerchantReqDto,
  GetListGroupCustomerMerchantReqDto,
} from '../../dtos/merchant/req/customer.merchant.req.dto';
import { User } from '../../entities/user.entity';
import { UserGroupMerchantService } from '../../services/merchant/user-group.merchant.service';

@Controller(`${PrefixType.MERCHANT}/user-group`)
@ApiTags('User Group Merchant')
@AuthorizeMerchant({ action: Action.READ, resource: Resource.CUSTOMER })
export class UserGroupMerchantController {
  constructor(private userGroupMerchantService: UserGroupMerchantService) {}

  @Post()
  @PaginationResponse(UserGroupResDto)
  createGroupUser(
    @Body() body: CreateGroupCustomerMerchantReqDto,
    @CurrentAuthData() user: User,
  ) {
    return this.userGroupMerchantService.createGroupCustomer(body, user);
  }

  @Post('/all')
  @PaginationResponse(UserGroupResDto)
  createGroupAllUser(
    @Body() body: CreateGroupAllCustomerMerchantReqDto,
    @CurrentAuthData() user: User,
  ) {
    return this.userGroupMerchantService.createGroupAllCustomer(body, user);
  }

  @Put('/all/:id')
  editGroupAllUser(
    @Body() body: CreateGroupAllCustomerMerchantReqDto,
    @CurrentAuthData() user: User,
    @Param('id', ParseIntPipe) id: number,
  ) {
    return this.userGroupMerchantService.updateGroupCustomerAll(body, user, id);
  }

  @Put(':id')
  editGroupUser(
    @Body() body: CreateGroupCustomerMerchantReqDto,
    @CurrentAuthData() user: User,
    @Param('id', ParseIntPipe) id: number,
  ) {
    return this.userGroupMerchantService.updateGroupCustomer(body, user, id);
  }

  @Get()
  @PaginationResponse(UserGroupResDto)
  getList(
    @Query() body: GetListGroupCustomerMerchantReqDto,
    @CurrentAuthData() user: User,
  ) {
    return this.userGroupMerchantService.getListGroupCustomer(body, user);
  }

  @Get(':id')
  async getDetail(
    @Param('id', ParseIntPipe) id: number,
    @CurrentAuthData() user: User,
  ) {
    return this.userGroupMerchantService.getGroupCustomerDetail(id, user);
  }

  @Delete(':id')
  @AuthorizeMerchant({ action: Action.DELETE, resource: Resource.CUSTOMER })
  deleteSingle(
    @Param('id', ParseIntPipe) id: number,
    @CurrentAuthData() user: User,
  ) {
    return this.userGroupMerchantService.deleteSingle(id, user);
  }

  @Delete('multiple')
  @AuthorizeMerchant({ action: Action.DELETE, resource: Resource.CUSTOMER })
  deleteMultiple(
    @Body() body: DeleteMultipleByIdNumberReqDto,
    @CurrentAuthData() user: User,
  ) {
    return this.userGroupMerchantService.deleteMultiple(body, user);
  }
}
