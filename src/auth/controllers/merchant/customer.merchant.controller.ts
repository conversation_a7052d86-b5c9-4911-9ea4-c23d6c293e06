import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Action, Resource } from 'src/common/enums/casl.enum';
import { PrefixType } from '../../../common/constants/global.constant';
import {
  AuthorizeMerchant,
  CurrentAuthData,
} from '../../../common/decorators/auth.decorator';
import { PaginationResponse } from '../../../common/decorators/swagger.decorator';
import { CustomerResDto } from '../../dtos/common/res/customer.res.dto';
import {
  BlockCustomerAccountReqDto,
  BlockCustomerScanQrCodeReqDto,
  GetListCustomerMerchantReqDto,
  UpdateCustomerMerchantReqDto,
} from '../../dtos/merchant/req/customer.merchant.req.dto';
import { User } from '../../entities/user.entity';
import { CustomerMerchantService } from '../../services/merchant/customer.merchant.service';

@Controller(`${PrefixType.MERCHANT}/customer`)
@ApiTags('Manage Customer Merchant')
@AuthorizeMerchant({ action: Action.READ, resource: Resource.CUSTOMER })
export class CustomerMerchantController {
  constructor(private customerMerchantService: CustomerMerchantService) {}

  @Get()
  @PaginationResponse(CustomerResDto)
  getList(
    @Query() body: GetListCustomerMerchantReqDto,
    @CurrentAuthData() user: User,
  ) {
    return this.customerMerchantService.getListCustomer(body, user);
  }

  @Get(':id')
  async getDetail(
    @Param('id', ParseIntPipe) id: number,
    @CurrentAuthData() user: User,
  ) {
    return this.customerMerchantService.getDetailCustomer(id, user);
  }

  @Post('request-export')
  requestExport(
    @Body() body: GetListCustomerMerchantReqDto,
    @CurrentAuthData() user: User,
  ) {
    return this.customerMerchantService.requestExport(body, user);
  }

  @Patch(':id')
  @AuthorizeMerchant({ action: Action.UPDATE, resource: Resource.CUSTOMER })
  async updateCustomerInfo(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: UpdateCustomerMerchantReqDto,
    @CurrentAuthData() user: User,
  ) {
    return this.customerMerchantService.updateCustomerInfo(id, user, body);
  }

  @Patch(':id/block-account')
  @AuthorizeMerchant({ action: Action.UPDATE, resource: Resource.CUSTOMER })
  async blockCustomerAccount(
    @CurrentAuthData() user: User,
    @Param('id', ParseIntPipe) id: number,
    @Body() body: BlockCustomerAccountReqDto,
  ) {
    return this.customerMerchantService.blockCustomerAccount(user, id, body);
  }

  @Patch(':id/block-scan-qr-code')
  @AuthorizeMerchant({ action: Action.UPDATE, resource: Resource.CUSTOMER })
  async blockCustomerScanQrCode(
    @CurrentAuthData() user: User,
    @Param('id', ParseIntPipe) id: number,
    @Body() body: BlockCustomerScanQrCodeReqDto,
  ) {
    return this.customerMerchantService.blockCustomerScanQrCode(user, id, body);
  }
}
