import { Body, Controller, Get, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { PrefixType } from '../../../common/constants/global.constant';
import {
  AuthenticateMerchant,
  CurrentAuthData,
} from '../../../common/decorators/auth.decorator';
import { RefreshTokenReqDto } from '../../dtos/common/req/auth.req.dto';
import {
  MerchantLoginReqDto,
  RegisterMerchantReqDto,
} from '../../dtos/merchant/req/auth.merchant.req.dto';
import { User } from '../../entities/user.entity';
import { AuthMerchantService } from '../../services/merchant/auth.merchant.service';

@Controller(`${PrefixType.MERCHANT}/auth`)
@ApiTags('Auth Merchant')
export class AuthMerchantController {
  constructor(private authMerchantService: AuthMerchantService) {}

  @Post('login')
  login(@Body() body: MerchantLoginReqDto) {
    return this.authMerchantService.login(body);
  }

  @Post('register')
  register(@Body() body: RegisterMerchantReqDto) {
    return this.authMerchantService.register(body);
  }

  @Get('current')
  @AuthenticateMerchant()
  getCurrent(@CurrentAuthData() user: User) {
    return this.authMerchantService.getCurrent(user);
  }

  @Post('refresh-token')
  refreshToken(@Body() body: RefreshTokenReqDto) {
    return this.authMerchantService.refreshToken(body);
  }
}
