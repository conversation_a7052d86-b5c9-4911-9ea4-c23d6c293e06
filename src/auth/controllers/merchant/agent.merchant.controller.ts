import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { PrefixType } from '../../../common/constants/global.constant';
import {
  AuthorizeMerchant,
  CurrentAuthData,
} from '../../../common/decorators/auth.decorator';
import { PaginationResponse } from '../../../common/decorators/swagger.decorator';
import { DeleteMultipleByIdNumberReqDto } from '../../../common/dtos/delete-multiple.dto';
import { Action, Resource } from '../../../common/enums/casl.enum';

import { MerchantResDto } from '../../dtos/common/res/merchant.res.dto';
import {
  CreateAgentMerchantReqDto,
  ListAgentMerchantReqDto,
  UpdateAgentMerchantReqDto,
} from '../../dtos/merchant/req/auth.merchant.req.dto';
import { User } from '../../entities/user.entity';
import { AgentMerchantService } from '../../services/merchant/agent.merchant.service';

@Controller(`${PrefixType.MERCHANT}/agent`)
@ApiTags('Manage Agent')
@AuthorizeMerchant({ action: Action.READ, resource: Resource.AGENT })
export class AgentMerchantController {
  constructor(private agentMerchantService: AgentMerchantService) {}

  @Post()
  @AuthorizeMerchant({ action: Action.CREATE, resource: Resource.AGENT })
  create(
    @Body() body: CreateAgentMerchantReqDto,
    @CurrentAuthData() user: User,
  ) {
    return this.agentMerchantService.create(body, user);
  }

  @Get()
  @PaginationResponse(MerchantResDto)
  getList(
    @Query() body: ListAgentMerchantReqDto,
    @CurrentAuthData() user: User,
  ) {
    return this.agentMerchantService.getList(body, user);
  }

  @Get(':id')
  getDetail(@Param('id') id: number, @CurrentAuthData() user: User) {
    return this.agentMerchantService.getDetail(id, user);
  }

  @Put()
  @AuthorizeMerchant({ action: Action.UPDATE, resource: Resource.AGENT })
  update(
    @Body() dto: UpdateAgentMerchantReqDto,
    @CurrentAuthData() user: User,
  ) {
    return this.agentMerchantService.update(dto, user);
  }

  @Delete()
  @AuthorizeMerchant({ action: Action.DELETE, resource: Resource.AGENT })
  deleteList(
    @Body() body: DeleteMultipleByIdNumberReqDto,
    @CurrentAuthData() user: User,
  ) {
    return this.agentMerchantService.deleteList(body, user);
  }

  @Delete(':id')
  @AuthorizeMerchant({ action: Action.DELETE, resource: Resource.AGENT })
  deleteSingle(@Param('id') id: number, @CurrentAuthData() user: User) {
    return this.agentMerchantService.deleteSingle(id, user);
  }
}
