import { Body, Controller, Get, Patch, Put } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { PrefixType } from '../../../common/constants/global.constant';
import {
  AuthenticateMerchant,
  CurrentAuthData,
} from '../../../common/decorators/auth.decorator';
import {
  ChangePasswordMerchantReqDto,
  UpdateProfileMerchantReqDto,
} from '../../dtos/merchant/req/profile.merchant.req.dto';
import { User } from '../../entities/user.entity';
import { ProfileMerchantService } from '../../services/merchant/profile.merchant.service';

@Controller(`${PrefixType.MERCHANT}/profile`)
@AuthenticateMerchant()
@ApiTags('Profile Merchant')
export class ProfileMerchantController {
  constructor(private profileMerchantService: ProfileMerchantService) {}

  @Get()
  getProfile(@CurrentAuthData() user: User) {
    return this.profileMerchantService.get(user);
  }

  @Put()
  updateProfile(
    @Body() body: UpdateProfileMerchantReqDto,
    @CurrentAuthData() user: User,
  ) {
    return this.profileMerchantService.update(body, user);
  }

  @Patch('password')
  updatePassword(
    @Body() body: ChangePasswordMerchantReqDto,
    @CurrentAuthData() user: User,
  ) {
    return this.profileMerchantService.changePassword(body, user);
  }
}
