import { Body, Controller, Get, Post } from '@nestjs/common';
import { Query } from '@nestjs/common/decorators';
import { ApiTags } from '@nestjs/swagger';
import { PrefixType } from '../../../common/constants/global.constant';
import {
  AuthenticateCustomer,
  CurrentAuthData,
  MerchantId,
  MerchantIdHeader,
} from '../../../common/decorators/auth.decorator';
import { RefreshTokenReqDto } from '../../dtos/common/req/auth.req.dto';
import {
  CheckPasswordCustomerReqDto,
  CheckPhoneNumberCustomerReqDto,
  LoginCustomerReqDto,
  RegisterCustomerReqDto,
  ResetPasswordCustomerReqDto,
  ZaloLoginByUserInfoCustomerReqDto,
  ZaloLoginCustomerReqDto,
} from '../../dtos/customer/req/auth.customer.req.dto';
import { User } from '../../entities/user.entity';
import { AuthCustomerService } from '../../services/customer/auth.customer.service';

@Controller(`${PrefixType.CUSTOMER}/auth`)
@ApiTags('Auth Customer')
export class AuthCustomerController {
  constructor(private authCustomerService: AuthCustomerService) {}

  @Post('register')
  @MerchantIdHeader()
  register(
    @Body() body: RegisterCustomerReqDto,
    @MerchantId() merchantId: number,
  ) {
    return this.authCustomerService.register(body, merchantId);
  }

  @Post('login')
  @MerchantIdHeader()
  login(@Body() body: LoginCustomerReqDto, @MerchantId() merchantId: number) {
    return this.authCustomerService.login(body, merchantId);
  }

  @Post('refresh-token')
  refreshToken(@Body() body: RefreshTokenReqDto) {
    return this.authCustomerService.refreshToken(body);
  }

  @Get('is-phone-existed')
  @MerchantIdHeader()
  checkPhoneNumber(
    @Query() query: CheckPhoneNumberCustomerReqDto,
    @MerchantId() merchantId: number,
  ) {
    return this.authCustomerService.checkPhoneNumber(query, merchantId);
  }

  @Post('check-password')
  @AuthenticateCustomer()
  checkPassword(
    @Body() body: CheckPasswordCustomerReqDto,
    @CurrentAuthData() user: User,
  ) {
    return this.authCustomerService.checkPassword(body, user);
  }

  @Get('current')
  @AuthenticateCustomer()
  getCurrent(@CurrentAuthData() user: User) {
    return this.authCustomerService.getCurrent(user);
  }

  @Post('reset-password')
  @MerchantIdHeader()
  resetPassword(
    @Body() body: ResetPasswordCustomerReqDto,
    @MerchantId() merchantId: number,
  ) {
    return this.authCustomerService.resetPassword(body, merchantId);
  }

  @Post('login/zalo-mini-app')
  @MerchantIdHeader()
  loginWithZaloMiniApp(
    @Body() body: ZaloLoginCustomerReqDto,
    @MerchantId() merchantId: number,
  ) {
    return this.authCustomerService.loginWithZaloMiniApp(body, merchantId);
  }

  @Post('login/zalo-mini-app/by-user-info')
  @MerchantIdHeader()
  loginWithZaloMiniAppByUserInfo(
    @Body() body: ZaloLoginByUserInfoCustomerReqDto,
    @MerchantId() merchantId: number,
  ) {
    return this.authCustomerService.loginWithZaloMiniAppByUserInfo(
      body,
      merchantId,
    );
  }
}
