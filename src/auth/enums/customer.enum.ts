export enum CustomerAuthProvider {
  BASIC = 'BASIC',
  ZALO = 'ZALO',
}

export enum CustomerStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

export enum CustomerGender {
  MALE = 'MALE',
  FEMALE = 'FEMALE',
}

export enum BlockStatus {
  UN_BLOCK = 'UN_BLOCK',
  BLOCK_ACCOUNT = 'BLOCK_ACCOUNT',
  BLOCK_SCAN_QR_CODE = 'BLOCK_SCAN_QR_CODE',
}

export enum SfAccountSource {
  LOYALTY_APP = 'LOYALTY_APP',
  SALE_FORCE = 'SALE_FORCE',
}

export enum CusotmerAccountStatus {
  ACTIVE = 'ACTIVE',
  BLOCKED = 'BLOCKED',
}
