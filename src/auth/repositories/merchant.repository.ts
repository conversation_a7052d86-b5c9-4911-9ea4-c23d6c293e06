import { Injectable } from '@nestjs/common';
import { DataSource, FindOptionsWhere } from 'typeorm';
import { BaseRepository } from '../../common/repositories/base.repositories';
import { I18nPath } from '../../i18n/i18n.generated';
import { Customer } from '../entities/customer.entity';
import { Merchant } from '../entities/merchant.entity';
import { MerchantStatus } from '../enums/merchant.enum';

@Injectable()
export class MerchantRepository extends BaseRepository<Merchant> {
  entityNameI18nKey: I18nPath;
  constructor(dataSource: DataSource) {
    super(Merchant, dataSource);
    this.entityNameI18nKey = 'common.word.merchant';
  }

  async getAndCheckMerchant(opts: FindOptionsWhere<Merchant>) {
    const merchant = await this.findOneByOrThrowNotFoundExc({
      status: MerchantStatus.APPROVED,
      ...opts,
    });

    return merchant;
  }

  async getRootMerchantId(merchant: Merchant) {
    if (!merchant.parentId) return merchant.id;

    let rootMerchant = merchant.parent;
    if (!rootMerchant) {
      rootMerchant = await this.findOneByOrThrowNotFoundExc({
        id: merchant.parentId,
      });
    }
    return rootMerchant.id;
  }

  async getRootMerchantUserId(merchant: Merchant) {
    if (!merchant.parentId) return merchant.userId;

    let rootMerchant = merchant.parent;
    if (!rootMerchant) {
      rootMerchant = await this.findOneByOrThrowNotFoundExc({
        id: merchant.parentId,
      });
    }
    return rootMerchant.userId;
  }

  async getMerchantUserIdOfCustomer(customer: Customer) {
    const merchant = await this.findOneByOrThrowNotFoundExc({
      userId: customer.merchantUserId,
    });

    return merchant.userId;
  }
}
