import { HttpModule } from '@nestjs/axios';
import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';
import { forwardRef } from '@nestjs/common/utils';
import { ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmCustomModule } from 'utility/dist';
import { BlockedHistoryRepository } from '../blacklist/repositories/blocked-history.repository';
import { GroupPolicyRepository } from '../casl/repositories/group-policy.repository';
import { UserToGroupPolicyRepository } from '../casl/repositories/user-to-group-policy.repository';
import { bullQueues } from '../common/config/bull.config';
import { GlobalConfig } from '../common/config/global.config';
import { FileModule } from '../file/file.module';
import { FileRepository } from '../file/repositories/file.repository';
import { UserPointRepository } from '../point/repositories/user-point.repository';
import { AppConfigRepository } from '../system-config/repositories/app-config.repository';
import { SecretRepository } from '../system-config/repositories/secret.repository';
import { SystemConfigRepository } from '../system-config/repositories/system-config.repository';
import { SystemConfigModule } from '../system-config/system-config.module';
import { UtilsModule } from '../utils/utils.module';
import { AuthCustomerController } from './controllers/customer/auth.customer.controller';
import { ProfileCustomerController } from './controllers/customer/profile.customer.controller';
import { AgentMerchantController } from './controllers/merchant/agent.merchant.controller';
import { AuthMerchantController } from './controllers/merchant/auth.merchant.controller';
import { CustomerMerchantController } from './controllers/merchant/customer.merchant.controller';
import { ProfileMerchantController } from './controllers/merchant/profile.merchant.controller';
import { UserGroupMerchantController } from './controllers/merchant/user-group.merchant.controller';
import { AdminRepository } from './repositories/admin.repository';
import { CustomerTokenRepository } from './repositories/customer-token.repository';
import { CustomerRepository } from './repositories/customer.repository';
import { MerchantRepository } from './repositories/merchant.repository';
import { UserGroupToUserRepository } from './repositories/user-group-to-user.repository';
import { UserGroupRepository } from './repositories/user-group.repository';
import { UserRepository } from './repositories/user.repository';
import { AdminAdminService } from './services/admin/admin.admin.service';
import { AuthAdminService } from './services/admin/auth.admin.service';
import { CustomerAdminService } from './services/admin/customer.admin.service';
import { MerchantAdminService } from './services/admin/merchant.admin.service';
import { ProfileAdminService } from './services/admin/profile.admin.service';
import { AuthCommonService } from './services/common/auth.common.service';
import { AuthCustomerService } from './services/customer/auth.customer.service';
import { ProfileCustomerService } from './services/customer/profile.customer.service';
import { UserGroupCustomerService } from './services/customer/user-group.customer.service';
import { AgentMerchantService } from './services/merchant/agent.merchant.service';
import { AuthMerchantService } from './services/merchant/auth.merchant.service';
import { CustomerMerchantService } from './services/merchant/customer.merchant.service';
import { ProfileMerchantService } from './services/merchant/profile.merchant.service';
import { UserGroupMerchantService } from './services/merchant/user-group.merchant.service';
import { JwtAuthenAdminStrategy } from './strategies/jwt-authen.admin.strategy';
import { JwtAuthenCustomerStrategy } from './strategies/jwt-authen.customer.strategy';
import { JwtAuthenMerchantStrategy } from './strategies/jwt-authen.merchant.strategy';
import { JwtAuthenUserStrategy } from './strategies/jwt-authen.user.strategy';

@Module({
  imports: [
    HttpModule,
    PassportModule,
    JwtModule.registerAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService<GlobalConfig>) => ({
        secret: configService.get('auth.accessToken.secret'),
        signOptions: {
          algorithm: configService.get('auth.accessToken.algorithm'),
        },
      }),
    }),
    BullModule.registerQueue(...bullQueues),
    TypeOrmCustomModule.forFeature([
      MerchantRepository,
      UserRepository,
      CustomerRepository,
      AdminRepository,
      CustomerTokenRepository,
      UserGroupToUserRepository,
      UserGroupRepository,
      GroupPolicyRepository,
      UserToGroupPolicyRepository,
      FileRepository,
      SystemConfigRepository,
      AppConfigRepository,
      SecretRepository,
      UserPointRepository,
      BlockedHistoryRepository,
    ]),
    UtilsModule,
    forwardRef(() => FileModule),
    SystemConfigModule,
  ],
  controllers: [
    AuthMerchantController,
    // AuthAdminController,
    AuthCustomerController,
    // AdminAdminController,
    // MerchantAdminController,
    // CustomerAdminController,
    ProfileMerchantController,
    // ProfileAdminController,
    ProfileCustomerController,
    AgentMerchantController,
    CustomerMerchantController,
    UserGroupMerchantController,
  ],
  providers: [
    JwtAuthenMerchantStrategy,
    JwtAuthenAdminStrategy,
    JwtAuthenCustomerStrategy,
    JwtAuthenUserStrategy,
    AuthAdminService,
    AuthMerchantService,
    AuthCustomerService,
    AdminAdminService,
    MerchantAdminService,
    CustomerAdminService,
    ProfileMerchantService,
    ProfileAdminService,
    ProfileCustomerService,
    AuthCommonService,
    AgentMerchantService,
    CustomerMerchantService,
    UserGroupMerchantService,
    UserGroupCustomerService,
  ],
  exports: [CustomerMerchantService],
})
export class AuthModule {}
