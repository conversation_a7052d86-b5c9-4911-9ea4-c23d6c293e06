import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { BaseEntityWithoutUpdateAndVersion } from '../../common/entities/base.entity';
import { CustomerTokenType } from '../enums/customer-token.enum';
import { Merchant } from './merchant.entity';

@Entity()
export class CustomerToken extends BaseEntityWithoutUpdateAndVersion {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ enum: CustomerTokenType, type: 'enum' })
  type: CustomerTokenType;

  @Column({ length: 256 })
  token: string;

  @Column({ nullable: true, type: 'timestamptz' })
  expiresAt: Date;

  @Column({ length: 255 })
  phoneNumber: string;

  // Join merchant
  @Column()
  merchantId: number;

  @ManyToOne(() => Merchant, (m) => m.userTokens)
  @JoinColumn()
  merchant: Merchant;
  // End join merchant
}
