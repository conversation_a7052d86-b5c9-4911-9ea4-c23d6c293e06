import { BaseEntity } from 'src/common/entities/base.entity';
import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { UniqueWithSoftDelete } from '../../common/decorators/typeorm.decorator';
import { File } from '../../file/entities/file.entity';
import { MerchantRank, MerchantStatus } from '../enums/merchant.enum';
import { CustomerToken } from './customer-token.entity';
import { User } from './user.entity';

@Entity('merchant')
export class Merchant extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  name: string;

  @Column()
  @UniqueWithSoftDelete()
  email: string;

  @Column({ select: false })
  password: string;

  @Column({ type: 'enum', enum: MerchantStatus })
  status: MerchantStatus;

  @Column({ type: 'enum', enum: MerchantRank, default: MerchantRank.BASIC })
  rank: MerchantRank;

  @Column({ nullable: true })
  address: string;

  @Column({ name: 'phone_number', length: 50, nullable: true })
  phoneNumber: string;

  // Join user
  @Column({ name: 'user_id' })
  userId: number;

  @OneToOne(() => User, (user) => user.merchant)
  @JoinColumn({ name: 'user_id' })
  user: User;
  // End join user

  // Join file
  @Column({ name: 'avatar_id', nullable: true })
  avatarId: number;

  @OneToOne(() => File)
  @JoinColumn({ name: 'avatar_id' })
  avatar: File;
  // End join file

  // join merchant
  @OneToMany(() => Merchant, (merchant) => merchant.agents)
  agents: Merchant[];

  @Column({ nullable: true })
  parentId?: number;

  @ManyToOne(() => Merchant, (agent) => agent.parent)
  parent: Merchant;
  // end join merchant

  // Join user_token
  @OneToMany(() => CustomerToken, (ut) => ut.merchant)
  userTokens: CustomerToken[];
  // End join user_token
}
