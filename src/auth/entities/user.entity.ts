import { BaseEntity } from 'src/common/entities/base.entity';
import {
  Column,
  Entity,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { GroupPolicy } from '../../casl/entities/group-policy.entity';
import { UserToGroupPolicy } from '../../casl/entities/user-to-group-policy.entity';
import { File } from '../../file/entities/file.entity';
import { AppConfig } from '../../system-config/entities/app-config.entity';
import { Secret } from '../../system-config/entities/secret.entity';
import { SystemConfig } from '../../system-config/entities/system-config.entity';
import { UserType } from '../enums/user.enum';
import { Admin } from './admin.entity';
import { Customer } from './customer.entity';
import { Merchant } from './merchant.entity';
import { UserGroupToUser } from './user-group-to-user.entity';
import { UserGroup } from './user-group.entity';

@Entity({ name: 'user' })
export class User extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'enum', enum: UserType, default: UserType.MERCHANT })
  type: UserType;

  @OneToOne(() => Merchant, (merchant) => merchant.user)
  merchant: Merchant;

  @OneToOne(() => Admin, (admin) => admin.user)
  admin: Admin;

  @OneToOne(() => Customer, (customer) => customer.user)
  customer: Customer;

  @OneToMany(
    () => UserToGroupPolicy,
    (userToGroupPolicies) => userToGroupPolicies.user,
  )
  userToGroupPolicies: UserToGroupPolicy[];

  @OneToMany(() => File, (file) => file.uploader)
  files: File[];

  @OneToMany(() => Customer, (customer) => customer.merchantUser)
  customersOfMerchant: Customer[];

  @OneToMany(() => GroupPolicy, (groupPolicy) => groupPolicy.owner)
  groupPolicies: GroupPolicy[];

  @OneToMany(() => UserGroupToUser, (userGroupToUse) => userGroupToUse.user)
  userGroupToUsers: UserGroupToUser[];

  @OneToMany(() => UserGroup, (userGroup) => userGroup.owner)
  userGroups: UserGroup[];

  @OneToMany(() => AppConfig, (appConfig) => appConfig.onwer)
  appConfigs: AppConfig[];

  @OneToMany(() => SystemConfig, (systemConfig) => systemConfig.owner)
  systemConfigs: SystemConfig[];

  @OneToMany(() => Secret, (secret) => secret.owner)
  secrets: Secret[];
}
