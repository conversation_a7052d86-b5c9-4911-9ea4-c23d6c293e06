import { BaseEntity } from 'src/common/entities/base.entity';
import {
  Column,
  Entity,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { BlockedHistory } from '../../blacklist/entities/blocked-history.entity';
import { Cart } from '../../cart/entities/cart.entity';
import { GroupPolicy } from '../../casl/entities/group-policy.entity';
import { UserToGroupPolicy } from '../../casl/entities/user-to-group-policy.entity';
import { Category } from '../../category/entities/category.entity';
import { RequestExport } from '../../export/entities/request-export.entity';
import { IrisTransactionLog } from '../../external/entities/iris-transaction-log.entity';
import { FileRequestDownloadHistory } from '../../file-request/entities/file-request-download-history.entity';
import { FileRequest } from '../../file-request/entities/file-request.entity';
import { File } from '../../file/entities/file.entity';
import { NotificationToUser } from '../../notification/entities/notification-to-user.entity';
import { Notification } from '../../notification/entities/notification.entity';
import { Order } from '../../order/entities/order.entity';
import { UserHistoryPoint } from '../../point/entities/user-history-point.entity';
import { UserPoint } from '../../point/entities/user-point.entity';
import { Product } from '../../product/entities/product.entity';
import { QrCode } from '../../qr-code/entities/qr-code.entity';
import { ScanHistory } from '../../qr-code/entities/scan-history.entity';
import { UserNumberScan } from '../../qr-code/entities/user-number-scan.entity';
import { AppConfig } from '../../system-config/entities/app-config.entity';
import { Secret } from '../../system-config/entities/secret.entity';
import { SystemConfig } from '../../system-config/entities/system-config.entity';
import { UserType } from '../enums/user.enum';
import { Admin } from './admin.entity';
import { Customer } from './customer.entity';
import { Merchant } from './merchant.entity';
import { UserGroupToUser } from './user-group-to-user.entity';
import { UserGroup } from './user-group.entity';

@Entity({ name: 'user' })
export class User extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'enum', enum: UserType, default: UserType.MERCHANT })
  type: UserType;

  @OneToOne(() => Merchant, (merchant) => merchant.user)
  merchant: Merchant;

  @OneToOne(() => Admin, (admin) => admin.user)
  admin: Admin;

  @OneToOne(() => Customer, (customer) => customer.user)
  customer: Customer;

  @OneToMany(
    () => UserToGroupPolicy,
    (userToGroupPolicies) => userToGroupPolicies.user,
  )
  userToGroupPolicies: UserToGroupPolicy[];

  @OneToMany(() => File, (file) => file.uploader)
  files: File[];

  @OneToMany(() => Customer, (customer) => customer.merchantUser)
  customersOfMerchant: Customer[];

  @OneToMany(() => GroupPolicy, (groupPolicy) => groupPolicy.owner)
  groupPolicies: GroupPolicy[];

  @OneToMany(() => UserGroupToUser, (userGroupToUse) => userGroupToUse.user)
  userGroupToUsers: UserGroupToUser[];

  @OneToMany(() => UserGroup, (userGroup) => userGroup.owner)
  userGroups: UserGroup[];

  @OneToMany(() => AppConfig, (appConfig) => appConfig.onwer)
  appConfigs: AppConfig[];

  @OneToMany(() => SystemConfig, (systemConfig) => systemConfig.owner)
  systemConfigs: SystemConfig[];

  @OneToMany(() => Secret, (secret) => secret.owner)
  secrets: Secret[];

  @OneToMany(() => Category, (category) => category.owner)
  categories: Category[];

  @OneToMany(() => Product, (product) => product.owner)
  products: Product[];

  @OneToMany(() => FileRequest, (fileRequest) => fileRequest.userRequest)
  requestFiles: FileRequest[];

  @OneToMany(() => FileRequest, (fileRequest) => fileRequest.userApprove)
  approveFiles: FileRequest[];

  @OneToMany(() => FileRequest, (fileRequest) => fileRequest.owner)
  fileRequests: FileRequest[];

  @OneToMany(() => QrCode, (qrCode) => qrCode.owner)
  qrCodes: QrCode[];

  @OneToOne(() => UserNumberScan, (uns) => uns.user)
  userNumberScan: UserNumberScan;

  @OneToMany(() => FileRequestDownloadHistory, (fdh) => fdh.user)
  fileDownloadHistories: FileRequestDownloadHistory[];

  @OneToMany(() => ScanHistory, (scanHistory) => scanHistory.user)
  scanHistories: ScanHistory[];

  @OneToMany(() => RequestExport, (re) => re.owner)
  requestExports: RequestExport[];

  @OneToMany(() => BlockedHistory, (blockedHistory) => blockedHistory.user)
  blockedHistories: BlockedHistory[];

  @OneToMany(() => UserHistoryPoint, (uhp) => uhp.user)
  userHistoryPoints: UserHistoryPoint[];

  @OneToOne(() => UserPoint, (up) => up.user)
  userPoint: UserPoint;

  @OneToMany(() => Cart, (cart) => cart.user)
  carts: Cart[];

  @OneToMany(() => Order, (order) => order.user)
  orders: Order[];

  @OneToMany(() => Notification, (noitificaiton) => noitificaiton.sender)
  notifications: Notification[];

  @OneToMany(() => NotificationToUser, (ntu) => ntu.user)
  notificationToUsers: NotificationToUser[];

  @OneToMany(() => IrisTransactionLog, (itl) => itl.user)
  irisTransactionLogs: IrisTransactionLog[];
}
