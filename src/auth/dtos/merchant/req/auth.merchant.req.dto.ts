import {
  IsValidArrayNumber,
  IsValidEmail,
  IsValidEnum,
  IsValidNumber,
  IsValidText,
} from '../../../../common/decorators/custom-validator.decorator';
import { PaginationReqDto } from '../../../../common/dtos/pagination.dto';
import { I18nPath } from '../../../../i18n/i18n.generated';
import { MerchantRank, MerchantStatus } from '../../../enums/merchant.enum';

export class MerchantLoginReqDto {
  @IsValidEmail({ message: 'auth.merchant.invalidEmail' })
  email: string;

  @IsValidText({
    minLength: 6,
    message: ['common.word.password', 'common.word.invalid'],
  })
  password: string;
}

export class RegisterMerchantReqDto {
  @IsValidEmail({ message: 'auth.merchant.invalidEmail' as I18nPath })
  email: string;

  @IsValidText({ minLength: 6, message: 'auth.merchant.invalidPassword' })
  password: string;
}

export class CreateAgentMerchantReqDto {
  @IsValidEmail({ message: 'auth.merchant.invalidEmail' as I18nPath })
  email: string;

  @IsValidText({ minLength: 6, message: 'auth.merchant.invalidPassword' })
  password: string;

  @IsValidArrayNumber({
    required: true,
    minSize: 1,
    unique: true,
    message: 'auth',
  })
  groupPolicyIds: number[];
}

export class UpdateProfileMerchantReqDto {
  @IsValidText({ required: false, message: 'auth.merchant.invalidName' })
  name?: string;

  @IsValidText({ required: false, message: 'auth.merchant.invalidAddress' })
  address?: string;

  @IsValidText({ required: false, message: 'auth.merchant.invalidPhoneNumber' })
  phoneNumber?: string;

  @IsValidNumber({ min: 0, required: false })
  avatarId?: number;
}

export class ListAgentMerchantReqDto extends PaginationReqDto {
  @IsValidEnum({ enum: MerchantStatus, required: false })
  status?: MerchantStatus;

  @IsValidEnum({ enum: MerchantRank, required: false })
  rank?: MerchantRank;

  @IsValidText({ required: false, trim: true })
  searchText?: string;
}

export class UpdateAgentMerchantReqDto {
  @IsValidNumber({ required: true, min: 1 })
  id: number;

  @IsValidEnum({ enum: MerchantStatus, required: true })
  status: MerchantStatus;

  @IsValidArrayNumber({ required: true, minSize: 1, unique: true })
  groupPolicyIds: number[];
}
