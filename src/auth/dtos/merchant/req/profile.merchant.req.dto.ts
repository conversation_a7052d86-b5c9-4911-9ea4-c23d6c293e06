import {
  IsValidNumber,
  IsValidText,
} from '../../../../common/decorators/custom-validator.decorator';

export class UpdateProfileMerchantReqDto {
  @IsValidText({ required: false })
  name?: string;

  @IsValidText({ required: false })
  address?: string;

  @IsValidText({ required: false })
  phoneNumber?: string;

  @IsValidNumber({ min: 0, required: false })
  avatarId?: number;
}

export class ChangePasswordMerchantReqDto {
  @IsValidText({ minLength: 6 })
  password: string;

  @IsValidText({ minLength: 6 })
  newPassword: string;
}
