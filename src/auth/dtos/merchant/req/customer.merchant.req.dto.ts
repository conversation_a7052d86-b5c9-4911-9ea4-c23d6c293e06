import {
  IsValidArrayNumber,
  IsValidBoolean,
  IsValidDate,
  IsValidEmail,
  IsValidEnum,
  IsValidNumber,
  IsValidText,
} from '../../../../common/decorators/custom-validator.decorator';
import { PaginationReqDto } from '../../../../common/dtos/pagination.dto';
import { CustomerGender } from '../../../enums/customer.enum';

import { UserGroupStatus } from '../../../enums/user-group.enum';

export class BlockCustomerAccountReqDto {
  @IsValidBoolean()
  blockAccount: boolean;
}

export class BlockCustomerScanQrCodeReqDto {
  @IsValidBoolean()
  blockScanQrCode: boolean;
}

export class UpdateCustomerMerchantReqDto {
  @IsValidBoolean({ required: false })
  blockAccount?: boolean;

  @IsValidBoolean({ required: false })
  blockScanQrCode?: boolean;

  @IsValidNumber({ required: false })
  totalPoints?: number;

  @IsValidEmail({ required: false })
  email?: string;

  @IsValidText({ required: false })
  name?: string;

  @IsValidText({ required: false })
  address?: string;

  @IsValidDate({ required: false })
  birthDate?: Date;

  @IsValidEnum({ enum: CustomerGender, required: false })
  gender?: CustomerGender;

  @IsValidNumber({ min: 1, required: false })
  provinceId?: number;

  @IsValidNumber({ min: 1, required: false })
  districtId?: number;

  @IsValidNumber({ min: 1, required: false })
  wardId?: number;
}

export class GetListCustomerMerchantReqDto extends PaginationReqDto {
  @IsValidText({ required: false, trim: true })
  email?: string;

  @IsValidText({ required: false, trim: true })
  phoneNumber?: string;

  @IsValidText({ required: false, trim: true })
  name?: string;
}

export class CreateGroupCustomerMerchantReqDto {
  @IsValidText({ required: true, trim: true })
  name: string;

  @IsValidText({ required: true, trim: true })
  description: string;

  @IsValidEnum({ enum: UserGroupStatus, required: true })
  status: UserGroupStatus;

  @IsValidArrayNumber({ minValue: 1, minSize: 1 })
  ids: number[];
}

export class CreateGroupAllCustomerMerchantReqDto {
  @IsValidText({ required: true, trim: true })
  groupName: string;

  @IsValidText({ required: true, trim: true })
  description: string;

  @IsValidEnum({ enum: UserGroupStatus, required: true })
  status: UserGroupStatus;

  @IsValidText({ required: false, trim: true })
  email?: string;

  @IsValidText({ required: false, trim: true })
  phoneNumber?: string;

  @IsValidText({ required: false, trim: true })
  name?: string;
}

export class GetListGroupCustomerMerchantReqDto extends PaginationReqDto {}
