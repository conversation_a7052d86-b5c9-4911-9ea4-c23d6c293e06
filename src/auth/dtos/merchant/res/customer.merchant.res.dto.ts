import * as dayjs from 'dayjs';
import { TIME_ZONE } from '../../../../common/constants/global.constant';
import { Customer } from '../../../entities/customer.entity';

export class ExportCustomerMerchantResDto {
  'STT': number;
  'Tên khách hàng': string;
  'Số điện thoại': string;
  'email': string;
  'Hạng thành viên': string;
  'Xu': number;
  'Trạng thái': string;
  'Ngày sinh': string;
  'Giới tính': string;
  'Địa chỉ': string;
  'Ngày đăng ký tài khoản': string;
  'Lần truy cập app cuối': string;
  'Lần quét mã cuối': string;

  static fromCustomer({ stt, data }: { data: Customer; stt: number }) {
    const result = new ExportCustomerMerchantResDto();

    result['STT'] = stt;
    result['Tên khách hàng'] = data.name;
    result['Số điện thoại'] = data.phoneNumber;
    result['email'] = data.email;
    result['Trạng thái'] = data.status;
    result['Ngày sinh'] = dayjs
      .tz(data.birthDate, TIME_ZONE)
      .format('DD-MM-YYYY HH:mm');
    result['Giới tính'] = data.gender;
    result['Địa chỉ'] = data.address;
    result['Ngày đăng ký tài khoản'] = dayjs
      .tz(data.createdAt)
      .format('DD-MM-YYYY HH:mm');
    result['Lần truy cập app cuối'] = dayjs
      .tz(data.lastVisitDate)
      .format('DD-MM-YYYY HH:mm');
    result['Lần quét mã cuối'] = dayjs
      .tz(data.lastScanDate)
      .format('DD-MM-YYYY HH:mm');

    return result;
  }
}
