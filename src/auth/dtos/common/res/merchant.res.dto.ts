import { BaseResponseDtoParams } from '../../../../common/dtos/base.res';
import { FileResDto } from '../../../../file/dtos/res/file.res.dto';
import { Merchant } from '../../../entities/merchant.entity';
import { MerchantRank, MerchantStatus } from '../../../enums/merchant.enum';
import { UserResDto } from './user.res.dto';

export interface MerchantResDtoParams extends BaseResponseDtoParams {
  data: Merchant;
}

export class MerchantResDto {
  id: number;
  name: string;
  email: string;
  status: MerchantStatus;
  rank: MerchantRank;
  address: string;
  phoneNumber: string;
  user?: UserResDto;
  avatar?: FileResDto;
  parent?: MerchantResDto;
  agents: MerchantResDto[];
  isRootAccount = false;

  static mapProperty(dto: MerchantResDto, { data }: MerchantResDtoParams) {
    dto.id = data.id;
    dto.name = data.name;
    dto.email = data.email;
    dto.rank = data.rank;
    dto.status = data.status;
    dto.address = data.address;
    dto.phoneNumber = data.phoneNumber;
    dto.isRootAccount = !data.parentId;
  }

  static forMerchant(params: MerchantResDtoParams) {
    const { data, resOpts } = params;

    if (!data) return null;
    const result = new MerchantResDto();

    this.mapProperty(result, params);
    result.avatar = FileResDto.forMerchant({ data: data.avatar, resOpts });
    result.user = UserResDto.forMerchant({ data: data.user, resOpts });
    result.parent = MerchantResDto.forMerchant({ data: data.parent, resOpts });
    result.agents = data.agents?.map((item) =>
      MerchantResDto.forMerchant({ data: item, resOpts }),
    );
    return result;
  }

  static forAdmin(params: MerchantResDtoParams) {
    const { data, resOpts } = params;

    if (!data) return null;
    const result = new MerchantResDto();

    this.mapProperty(result, params);

    result.status = data.status;
    result.rank = data.rank;
    result.user = UserResDto.forAdmin({ data: data.user, resOpts });
    result.avatar = FileResDto.forAdmin({ data: data.avatar, resOpts });
    result.parent = MerchantResDto.forAdmin({ data: data.parent, resOpts });
    result.agents = data.agents?.map((item) =>
      MerchantResDto.forAdmin({ data: item, resOpts }),
    );

    return result;
  }
}
