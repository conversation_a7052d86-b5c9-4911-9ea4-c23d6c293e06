import { PolicyResDto } from '../../../../casl/dtos/common/res/policy.res.dto';
import { BaseResponseDtoParams } from '../../../../common/dtos/base.res';
import { FileResDto } from '../../../../file/dtos/res/file.res.dto';
import { Admin } from '../../../entities/admin.entity';
import { AdminStatus } from '../../../enums/admin.enum';
import { UserResDto } from './user.res.dto';

export interface AdminResDtoParams extends BaseResponseDtoParams {
  data: Admin;
}

export class AdminResDto {
  id: number;
  username: string;
  status: AdminStatus;
  name: string;
  avatar: FileResDto;
  user: UserResDto;
  policies: PolicyResDto[];

  private static mapProperty(dto: AdminResDto, { data }: AdminResDtoParams) {
    dto.id = data.id;
    dto.name = data.name;
    dto.username = data.username;
  }

  static forAdmin(params: AdminResDtoParams) {
    const { data, resOpts } = params;

    if (!data) return null;
    const result = new AdminResDto();

    this.mapProperty(result, params);
    this.mapPolicies(result, params);

    result.status = data.status;
    result.avatar = FileResDto.forAdmin({ data: data.avatar, resOpts });
    result.user = UserResDto.forAdmin({ data: data.user, resOpts });

    return result;
  }

  private static mapPolicies(
    dto: AdminResDto,
    { data, resOpts }: AdminResDtoParams,
  ) {
    dto.policies = [];
    if (!data?.user?.userToGroupPolicies) return;

    for (const userToGroupPolicy of data.user.userToGroupPolicies) {
      if (!userToGroupPolicy?.groupPolicy?.groupToPolicies) continue;

      const groupToPolicies = userToGroupPolicy.groupPolicy.groupToPolicies;

      for (const groupToPolicy of groupToPolicies) {
        dto.policies.push(
          PolicyResDto.forAdmin({ data: groupToPolicy.policy, resOpts }),
        );
      }
    }
  }
}
