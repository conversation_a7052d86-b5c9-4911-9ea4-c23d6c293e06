import {
  BaseResponseDtoParams,
  ResOptionDto,
} from '../../../../common/dtos/base.res';
import { Customer } from '../../../entities/customer.entity';
import { CustomerGender, CustomerStatus } from '../../../enums/customer.enum';
import { MerchantResDto } from './merchant.res.dto';
import { UserResDto } from './user.res.dto';

export interface CustomerResDtoParams extends BaseResponseDtoParams {
  dto?: CustomerResDto;
  data?: Customer;
  resOpts?: ResOptionDto;
  totalPoint?: number;
  blockAccount?: boolean;
  blockAddCode?: boolean;
}

export class CustomerResDto {
  id: number;
  phoneNumber: string;
  email: string;
  name: string;
  address: string;
  birthDate: Date;
  status: CustomerStatus;
  user: UserResDto;
  userId: number;
  merchant: MerchantResDto;
  lastVisitDate: Date;
  lastScanDate: Date;
  gender: CustomerGender;
  createdAt: Date;
  totalPoint: number;
  userZaloId: string;
  avatarUrl: string;
  blockAccount: boolean;
  blockAddCode: boolean;

  static mapProperty({
    dto,
    data,
    totalPoint,
    blockAccount,
    blockAddCode,
  }: CustomerResDtoParams) {
    dto.id = data.id;
    dto.phoneNumber = data.phoneNumber;
    dto.email = data.email;
    dto.name = data.name;
    dto.address = data.address;
    dto.birthDate = data.birthDate;
    dto.lastVisitDate = data.lastVisitDate;
    dto.lastScanDate = data.lastScanDate;
    dto.createdAt = data.createdAt;
    dto.gender = data.gender;
    dto.userId = data.userId;
    dto.totalPoint = totalPoint || 0;
    dto.userZaloId = data.userZaloId;
    dto.avatarUrl = data.avatarUrl;
    dto.blockAccount = blockAccount;
    dto.blockAddCode = blockAddCode;
  }

  static forCustomer({ data, totalPoint, resOpts }: CustomerResDtoParams) {
    if (!data) return null;

    const result = new CustomerResDto();

    this.mapProperty({
      dto: result,
      data: data,
      totalPoint: totalPoint,
    });

    return result;
  }

  static forMerchant({
    data,
    totalPoint,
    blockAccount,
    blockAddCode,
    resOpts,
  }: CustomerResDtoParams) {
    if (!data) return null;

    const result = new CustomerResDto();

    this.mapProperty({
      dto: result,
      data: data,
      totalPoint: totalPoint,
      blockAccount: blockAccount,
      blockAddCode: blockAddCode,
    });

    result.status = data.status;

    return result;
  }

  static forAdmin(params: CustomerResDtoParams) {
    const { data, resOpts } = params;

    const result = new CustomerResDto();
    if (!data) return null;

    this.mapProperty({ dto: result, data: data });

    result.status = data.status;

    result.user = UserResDto.forAdmin({ data: data.user, resOpts });
    result.merchant = MerchantResDto.forAdmin({
      data: data.merchantUser?.merchant,
      resOpts,
    });

    return result;
  }
}
