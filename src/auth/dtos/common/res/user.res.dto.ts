import { GroupPolicyResDto } from '../../../../casl/dtos/common/res/group-policies.res.dto';
import { ResOptionDto } from '../../../../common/dtos/base.res';

import { User } from '../../../entities/user.entity';

import { UserType } from '../../../enums/user.enum';

import { AdminResDto } from './admin.res.dto';

import { CustomerResDto } from './customer.res.dto';

import { MerchantResDto } from './merchant.res.dto';

export type UserResDtoParams = {
  data?: User;
  resOpts?: ResOptionDto;
  blockAccount?: boolean;
  blockAddCode?: boolean;
};

export class UserResDto {
  id: number;
  type: UserType;
  groupPolicies: GroupPolicyResDto[];
  customer: CustomerResDto;
  merchant: MerchantResDto;
  admin: AdminResDto;

  static mapProperty(dto: UserResDto, params: UserResDtoParams) {
    const { data } = params;

    dto.id = data.id;
  }

  static forMerchant(params: UserResDtoParams) {
    const { blockAccount, blockAddCode, data } = params;

    const result = new UserResDto();
    if (!data) return null;

    this.mapProperty(result, params);
    result.type = data.type;

    result.groupPolicies = data.userToGroupPolicies
      ?.map((item) => GroupPolicyResDto.forMerchant({ data: item.groupPolicy }))
      ?.filter(Boolean);
    result.customer = CustomerResDto.forMerchant({
      data: data.customer,
    });

    return result;
  }

  static forAdmin(params: UserResDtoParams) {
    const { data, resOpts } = params;

    const result = new UserResDto();
    if (!data) return null;

    this.mapProperty(result, params);

    result.type = data.type;
    result.groupPolicies = data.userToGroupPolicies
      ?.map((item) => GroupPolicyResDto.forAdmin({ data: item.groupPolicy }))
      .filter(Boolean);

    result.customer = CustomerResDto.forAdmin({ data: data.customer });

    result.merchant = MerchantResDto.forAdmin({ data: data.merchant });
    result.admin = AdminResDto.forAdmin({ data: data.admin });

    return result;
  }
}
