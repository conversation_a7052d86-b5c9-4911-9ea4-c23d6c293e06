import { Transform } from 'class-transformer';
import { IsString } from 'class-validator';
import { REGEX } from '../../../../common/constants/regex.constant';
import {
  IsValidEnum,
  IsValidText,
} from '../../../../common/decorators/custom-validator.decorator';
import { getPhoneE164 } from '../../../../common/utils';
import { CustomerAuthProvider } from '../../../enums/customer.enum';

export class RegisterCustomerReqDto {
  @IsValidText({
    message: 'auth.customer.wrongPhoneNumber',
    minLength: 12,
    maxLength: 12,
  })
  @Transform(({ value }) => getPhoneE164(value))
  phoneNumber: string;

  @IsValidText({
    minLength: 8,
    maxLength: 50,
    matches: REGEX.AT_LEAST_ONE_NUMBER_AND_CHARACTER,
    required: false,
  })
  password: string;

  @IsValidText({ maxLength: 30 })
  name: string;

  @IsValidEnum({
    enum: CustomerAuthProvider,
  })
  provider: CustomerAuthProvider;
}

export class LoginCustomerReqDto {
  @IsValidText({ message: 'auth.customer.wrongPhoneNumber' })
  @Transform(({ value }) => getPhoneE164(value))
  phoneNumber: string;

  @IsValidText({
    minLength: 8,
    maxLength: 255,
    message: 'auth.customer.wrongPassword',
    required: false,
  })
  password: string;

  @IsValidEnum({
    enum: CustomerAuthProvider,
  })
  provider: CustomerAuthProvider;
}

export class SaveFirTokenReqDto {
  @IsValidText({ required: false })
  deviceToken?: string;
}

export class AddFirTokenCustomerReqDto extends SaveFirTokenReqDto {}

export class DeleteFirTokenCustomerReqDto extends SaveFirTokenReqDto {}

export class ResetPasswordCustomerReqDto {
  @IsValidText({ message: 'auth.customer.wrongPhoneNumber' })
  @Transform(({ value }) => getPhoneE164(value))
  phoneNumber: string;

  @IsValidText()
  otp: string;

  @IsValidText({
    minLength: 8,
    maxLength: 50,
    matches: REGEX.AT_LEAST_ONE_NUMBER_AND_CHARACTER,
  })
  newPassword: string;
}

export class CheckPhoneNumberCustomerReqDto {
  @IsValidText({ message: 'auth.customer.wrongPhoneNumber' })
  @Transform(({ value }) => getPhoneE164(value))
  phoneNumber: string;
}

export class CheckPasswordCustomerReqDto {
  @IsValidText()
  password: string;
}

export class ZaloLoginCustomerReqDto {
  @IsString()
  accessToken: string;
}

export class ZaloLoginByUserInfoCustomerReqDto {
  @IsValidText()
  id: string;

  @IsValidText({ required: false })
  name: string;

  @IsValidText({ required: false })
  avatarUrl: string;
}
