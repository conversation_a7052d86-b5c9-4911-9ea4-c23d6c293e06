import { DEFAULT_REDIS, RedisService } from '@liaoliaots/nestjs-redis';
import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { Cron, CronExpression } from '@nestjs/schedule';
import * as dayjs from 'dayjs';
import { Redis } from 'ioredis';
import { lastValueFrom } from 'rxjs';
import { IsNull } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { GlobalConfig } from '../../../common/config/global.config';
import { ExceptionSubCode } from '../../../common/constants/exception.constant';
import { REDIS_KEY } from '../../../common/constants/redis.constant';
import {
  ConflictExc,
  ExpectationFailedExc,
  NotFoundExc,
  UnauthorizedExc,
} from '../../../common/exceptions/custom.exception';
import { calculateHMacSHA256 } from '../../../common/utils';
import { EncryptService } from '../../../utils/services/encrypt.service';
import {
  CHECK_PASSWORD_CONFIG,
  LOGIN_CONFIG,
} from '../../constants/index.constant';
import { RefreshTokenReqDto } from '../../dtos/common/req/auth.req.dto';
import { AuthTokenResDto } from '../../dtos/common/res/auth-token.res.dto';
import { CustomerResDto } from '../../dtos/common/res/customer.res.dto';
import {
  CheckPasswordCustomerReqDto,
  CheckPhoneNumberCustomerReqDto,
  LoginCustomerReqDto,
  RegisterCustomerReqDto,
  ResetPasswordCustomerReqDto,
  ZaloLoginByUserInfoCustomerReqDto,
  ZaloLoginCustomerReqDto,
} from '../../dtos/customer/req/auth.customer.req.dto';
import {
  CheckPasswordCustomerResDto,
  CheckPhoneNumberCustomerResDto,
} from '../../dtos/customer/res/auth.customer.res.dto';
import { CustomerToken } from '../../entities/customer-token.entity';
import { User } from '../../entities/user.entity';
import { CustomerTokenType } from '../../enums/customer-token.enum';
import {
  CustomerAuthProvider,
  CustomerStatus,
} from '../../enums/customer.enum';
import { MerchantStatus } from '../../enums/merchant.enum';
import { UserType } from '../../enums/user.enum';
import { JwtAuthPayload } from '../../interfaces/jwt-payload.interface';
import { CheckPasswordRedisData } from '../../interfaces/redis.interface';
import { CustomerTokenRepository } from '../../repositories/customer-token.repository';
import { CustomerRepository } from '../../repositories/customer.repository';
import { MerchantRepository } from '../../repositories/merchant.repository';
import { UserRepository } from '../../repositories/user.repository';
import { AuthCommonService } from '../common/auth.common.service';

@Injectable()
export class AuthCustomerService {
  private logger = new Logger(AuthCustomerService.name);
  private readonly redis: Redis | null;

  constructor(
    private jwtService: JwtService,
    private configService: ConfigService<GlobalConfig>,
    private encryptService: EncryptService,
    private authCommonService: AuthCommonService,
    private customerRepo: CustomerRepository,
    private userRepo: UserRepository,
    private merchantRepo: MerchantRepository,
    private customerTokenRepo: CustomerTokenRepository,
    private httpService: HttpService,
    private readonly redisService: RedisService,
  ) {
    this.redis = this.redisService.getOrNil(DEFAULT_REDIS);
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT, { utcOffset: 7 })
  async resetCheckPasswordRedisData(userId?: string) {
    try {
      if (userId) {
        await this.redis.hdel(REDIS_KEY.CHECK_PASSWORD_COUNT, userId);
      } else {
        await this.redis.del(REDIS_KEY.CHECK_PASSWORD_COUNT);
      }
    } catch (error) {
      this.logger.error(`Error resetCheckPasswordRedisData `);
      console.log('error', error);
    }
  }

  async checkPhoneNumber(
    dto: CheckPhoneNumberCustomerReqDto,
    merchantId: number,
  ) {
    const { phoneNumber } = dto;
    const merchant = await this.merchantRepo.findOneBy({
      id: merchantId,
      parentId: IsNull(),
      status: MerchantStatus.APPROVED,
    });

    const result = await this.customerRepo.findOneBy({
      phoneNumber,
      merchantUserId: merchant.userId,
    });

    return new CheckPhoneNumberCustomerResDto({ isExisted: Boolean(result) });
  }

  async checkPassword(dto: CheckPasswordCustomerReqDto, user: User) {
    const { password } = dto;

    const customer = user.customer;

    let { blockExp, retryTime } = await this.getCheckPasswordData(
      String(user.id),
    );

    const isBlocked = blockExp && dayjs(blockExp).isAfter(dayjs());

    let result = new CheckPasswordCustomerResDto({
      isCorrect: false,
      retryTime,
      blockExp: new Date(blockExp),
    });

    if (isBlocked) return result;

    const isCorrect = this.encryptService.compareHash(
      password,
      customer.password,
    );
    if (isCorrect) retryTime = 0;
    else retryTime += 1;

    if (retryTime >= CHECK_PASSWORD_CONFIG.MAX_RETRY_TIME) {
      blockExp = dayjs()
        .add(CHECK_PASSWORD_CONFIG.BLOCK_EXP, 'second')
        .toDate();
    }

    await this.saveCheckPasswordData({ blockExp, retryTime }, String(user.id));

    result = { blockExp, retryTime, isCorrect };

    return result;
  }

  async getCurrent(user: User) {
    const customer = await this.customerRepo.findOneOrThrowNotFoundExc({
      where: { userId: user.id },
    });

    return CustomerResDto.forCustomer({ data: customer });
  }

  async login(dto: LoginCustomerReqDto, merchantId: number) {
    const { password, phoneNumber } = dto;

    const merchant = await this.merchantRepo.getAndCheckMerchant({
      id: merchantId,
    });

    const customer = await this.customerRepo.findOne({
      where: { phoneNumber, merchantUserId: merchant.userId },
    });

    if (!customer)
      throw new UnauthorizedExc({ message: 'auth.customer.customerNotFound' });

    const redisKey = `${REDIS_KEY.LOGIN_COUNT}:${customer.id}`;
    const loginFailedDataStr = await this.redis.get(redisKey);

    let loginFailedData: LoginFailedRedisData = {
      blockExp: null,
      retryTime: 0,
    };

    if (loginFailedDataStr) loginFailedData = JSON.parse(loginFailedDataStr);

    const isBlocked =
      loginFailedData.blockExp && dayjs().isBefore(loginFailedData.blockExp);

    if (isBlocked) {
      throw new ExpectationFailedExc({
        message: 'auth.blockedLogin',
        params: { time: dayjs(loginFailedData.blockExp).format('HH:mm') },
        subCode: ExceptionSubCode.BLOCKED_LOGIN,
      });
    }

    if (loginFailedData.retryTime >= LOGIN_CONFIG.MAX_RETRY_TIME) {
      loginFailedData.blockExp = dayjs()
        .add(LOGIN_CONFIG.BLOCK_EXP, 'second')
        .toDate();

      await this.redis.set(
        redisKey,
        JSON.stringify(loginFailedData),
        'EX',
        LOGIN_CONFIG.BLOCK_EXP,
      );

      throw new ExpectationFailedExc({
        message: 'auth.blockedLogin',
        params: { time: dayjs(loginFailedData.blockExp).format('HH:mm') },
        subCode: ExceptionSubCode.BLOCKED_LOGIN,
      });
    }

    if (customer.provider === CustomerAuthProvider.BASIC) {
      if (!password) {
        throw new UnauthorizedExc({
          message: 'auth.customer.passwordRequired',
        });
      }

      if (!this.encryptService.compareHash(password, customer.password)) {
        loginFailedData.retryTime += 1;

        await this.redis.set(
          redisKey,
          JSON.stringify(loginFailedData),
          'EX',
          LOGIN_CONFIG.BLOCK_EXP,
        );

        throw new UnauthorizedExc({ message: 'auth.customer.failPassword' });
      }
    }

    customer.lastVisitDate = new Date();
    await this.customerRepo.save(customer);
    await this.redis.del(redisKey);

    const payload: JwtAuthPayload = { userId: customer.userId };
    const accessToken = this.authCommonService.generateAccessToken(payload);
    const refreshToken = this.authCommonService.generateRefreshToken(payload);

    return AuthTokenResDto.forCustomer({ data: { accessToken, refreshToken } });
  }

  @Transactional()
  async register(dto: RegisterCustomerReqDto, merchantId: number) {
    const { phoneNumber, password, name, provider } = dto;

    const merchant = await this.merchantRepo.getAndCheckMerchant({
      id: merchantId,
    });

    let customer = await this.customerRepo.findFirst({
      where: { phoneNumber, merchantUserId: merchant.userId },
    });
    if (customer) throw new ConflictExc({ message: 'auth.customer.existed' });

    const user = this.userRepo.create({ type: UserType.CUSTOMER });
    await this.userRepo.insert(user);

    if (provider === CustomerAuthProvider.BASIC) {
      if (!password)
        throw new ConflictExc({
          message: 'auth.customer.passwordRequired',
        });

      customer = this.customerRepo.create({
        phoneNumber,
        merchantUserId: merchant.userId,
        userId: user.id,
        name,
        password: this.encryptService.encryptText(password),
      });

      await this.customerRepo.insert(customer);
    }

    if (provider === CustomerAuthProvider.ZALO) {
      customer = this.customerRepo.create({
        phoneNumber,
        merchantUserId: merchant.userId,
        userId: user.id,
        name,
        provider,
      });

      await this.customerRepo.insert(customer);
    }

    const payload: JwtAuthPayload = { userId: customer.userId };
    const accessToken = this.authCommonService.generateAccessToken(payload);
    const refreshToken = this.authCommonService.generateRefreshToken(payload);

    return AuthTokenResDto.forCustomer({ data: { accessToken, refreshToken } });
  }

  async refreshToken(dto: RefreshTokenReqDto) {
    const { refreshToken } = dto;

    try {
      const payload = this.jwtService.verify<JwtAuthPayload>(refreshToken, {
        secret: this.configService.get('auth.refreshToken.secret'),
      });
      const accessToken = this.authCommonService.generateAccessToken({
        userId: payload.userId,
      });

      return AuthTokenResDto.forCustomer({ data: { accessToken } });
    } catch (error) {
      throw new UnauthorizedExc({
        subCode: ExceptionSubCode.INVALID_REFRESH_TOKEN,
        message: 'common',
      });
    }
  }

  @Transactional()
  async resetPassword(dto: ResetPasswordCustomerReqDto, merchantId: number) {
    const { phoneNumber, otp, newPassword } = dto;

    const { customer } = await this.getAndCheckCustomer({
      merchantId,
      phoneNumber,
    });
    const customerToken = await this.customerTokenRepo.findFirst({
      where: {
        type: CustomerTokenType.RESET_PASSWORD,
        phoneNumber,
        merchantId,
      },
      order: { createdAt: 'desc' },
    });

    this.checkCustomerToken({ otp, customerToken });

    await this.customerRepo.update(customer.id, {
      password: this.encryptService.encryptText(newPassword),
    });

    await this.resetCheckPasswordRedisData();

    const payload: JwtAuthPayload = { userId: customer.userId };
    const accessToken = this.authCommonService.generateAccessToken(payload);
    const refreshToken = this.authCommonService.generateRefreshToken(payload);

    return AuthTokenResDto.forCustomer({ data: { accessToken, refreshToken } });
  }

  async loginWithZaloMiniApp(dto: ZaloLoginCustomerReqDto, merchantId: number) {
    const merchant = await this.merchantRepo.getAndCheckMerchant({
      id: merchantId,
    });

    const params = '?fields=id,name,picture';
    const url = this.configService.get<string>('zalo.getMeUrl') + params;
    const appSecretKey = this.configService.get<string>('zalo.appSecretKey');

    const response = await lastValueFrom(
      this.httpService.get<{
        is_sensitive: boolean;
        id: string;
        error: number;
        message: string;
        user_id_by_oa: string;
      }>(url, {
        headers: {
          access_token: dto.accessToken,
          appsecret_proof: calculateHMacSHA256(dto.accessToken, appSecretKey),
        },
      }),
    );
    const { id: userZaloId, error, message } = response.data;
    if (error) {
      throw new ExpectationFailedExc({
        message: 'auth.customer.zaloLoginFailed',
        params: { message },
      });
    }

    const customer = await this.customerRepo.findOne({
      where: { userZaloId: userZaloId },
      relations: { merchantUser: true },
    });
    // Check if customer exists -> login
    if (customer) {
      if (customer.status !== CustomerStatus.ACTIVE)
        throw new ExpectationFailedExc({
          message: 'auth.customer.customerNotFound',
        });

      const payload: JwtAuthPayload = { userId: customer.userId };
      const accessToken = this.authCommonService.generateAccessToken(payload);
      const refreshToken = this.authCommonService.generateRefreshToken(payload);

      return AuthTokenResDto.forCustomer({
        data: { accessToken, refreshToken },
      });
    }

    // Check if customer exists -> register
    const user = this.userRepo.create({ type: UserType.CUSTOMER });
    await this.userRepo.insert(user);

    const newCustomer = this.customerRepo.create({
      merchantUserId: merchant.userId,
      userId: user.id,
      userZaloId,
      provider: CustomerAuthProvider.ZALO,
    });

    await this.customerRepo.insert(newCustomer);
    const payload: JwtAuthPayload = { userId: newCustomer.userId };
    const accessToken = this.authCommonService.generateAccessToken(payload);
    const refreshToken = this.authCommonService.generateRefreshToken(payload);
    return AuthTokenResDto.forCustomer({
      data: { accessToken, refreshToken },
    });
  }

  async loginWithZaloMiniAppByUserInfo(
    dto: ZaloLoginByUserInfoCustomerReqDto,
    merchantId: number,
  ) {
    const merchant = await this.merchantRepo.getAndCheckMerchant({
      id: merchantId,
    });

    const { id: userZaloId, name, avatarUrl } = dto;

    const customer = await this.customerRepo.findOne({
      where: { userZaloId: userZaloId },
      relations: { merchantUser: true },
    });
    // Check if customer exists -> login
    if (customer) {
      if (customer.status !== CustomerStatus.ACTIVE)
        throw new ExpectationFailedExc({
          message: 'auth.customer.customerNotFound',
        });

      const payload: JwtAuthPayload = { userId: customer.userId };
      const accessToken = this.authCommonService.generateAccessToken(payload);
      const refreshToken = this.authCommonService.generateRefreshToken(payload);

      return AuthTokenResDto.forCustomer({
        data: { accessToken, refreshToken },
      });
    }

    // Check if customer exists -> register
    const user = this.userRepo.create({ type: UserType.CUSTOMER });
    await this.userRepo.insert(user);

    const newCustomer = this.customerRepo.create({
      merchantUserId: merchant.userId,
      userId: user.id,
      userZaloId,
      name,
      avatarUrl,
      provider: CustomerAuthProvider.ZALO,
    });

    await this.customerRepo.insert(newCustomer);
    const payload: JwtAuthPayload = { userId: newCustomer.userId };
    const accessToken = this.authCommonService.generateAccessToken(payload);
    const refreshToken = this.authCommonService.generateRefreshToken(payload);
    return AuthTokenResDto.forCustomer({
      data: { accessToken, refreshToken },
    });
  }

  private async getCheckPasswordData(
    userId: string,
  ): Promise<CheckPasswordRedisData> {
    let checkPasswordData: CheckPasswordRedisData = {
      blockExp: null,
      retryTime: 0,
    };

    try {
      const redisValStr = await this.redis.hget(
        REDIS_KEY.CHECK_PASSWORD_COUNT,
        userId,
      );

      if (redisValStr) checkPasswordData = JSON.parse(redisValStr);
    } catch (error) {
      this.logger.error(`Error getCheckPasswordData from redis, `);

      this.logger.error(error);
    }

    return checkPasswordData;
  }

  private async saveCheckPasswordData(
    data: CheckPasswordRedisData,
    userId: string,
  ) {
    try {
      await this.redis.hset(
        REDIS_KEY.CHECK_PASSWORD_COUNT,
        userId,
        JSON.stringify(data),
      );
    } catch (error) {
      this.logger.error(
        `Error saveCheckPasswordData to redis, field: ${userId}, data: ${JSON.stringify(
          data,
        )}`,
      );

      console.log('error', error);
    }
  }

  private async getAndCheckCustomer({
    merchantId,
    phoneNumber,
  }: GetAndCheckCustomerParams) {
    const merchant = await this.merchantRepo.findOneByOrThrowNotFoundExc({
      id: merchantId,
    });

    const customer = await this.customerRepo.findFirst({
      where: {
        phoneNumber,
        merchantUserId: merchant.userId,
        status: CustomerStatus.ACTIVE,
      },
      order: { createdAt: 'DESC' },
    });
    if (!customer)
      throw new NotFoundExc({ message: 'auth.customer.customerNotFound' });

    return { customer, merchant };
  }

  private checkCustomerToken({ otp, customerToken }: CheckCustomerTokenParams) {
    if (!customerToken)
      throw new NotFoundExc({
        message: ['common.word.notFound', 'common.word.customerToken'],
      });

    if (customerToken.expiresAt < new Date())
      throw new ExpectationFailedExc({ message: 'token.otpExpires' });

    if (customerToken.token !== otp)
      throw new ExpectationFailedExc({ message: 'token.incorrectOtp' });
  }
}

interface GetAndCheckCustomerParams {
  phoneNumber: string;
  merchantId: number;
}

interface CheckCustomerTokenParams {
  customerToken: CustomerToken | null;
  otp: string;
}

interface LoginFailedRedisData {
  retryTime: number;
  blockExp: Date;
}
