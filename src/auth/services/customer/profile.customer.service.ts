import { Injectable } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { AppResponseDto } from '../../../common/dtos/app-response.dto';
import {
  ExpectationFailedExc,
  NotFoundExc,
} from '../../../common/exceptions/custom.exception';
import { UserPointRepository } from '../../../point/repositories/user-point.repository';
import { EncryptService } from '../../../utils/services/encrypt.service';
import { CustomerResDto } from '../../dtos/common/res/customer.res.dto';
import {
  UpdatePasswordCustomerReqDto,
  UpdateProfileCustomerReqDto,
} from '../../dtos/customer/req/profile.customer.req.dto';
import { User } from '../../entities/user.entity';
import { CustomerRepository } from '../../repositories/customer.repository';

@Injectable()
export class ProfileCustomerService {
  constructor(
    private encryptService: EncryptService,
    private customerRepo: CustomerRepository,
    private userPointRepo: UserPointRepository,
  ) {}

  async getProfile(user: User) {
    const customer = await this.customerRepo.findOneOrThrowNotFoundExc({
      where: { userId: user.id },
    });

    const userPoint = await this.userPointRepo.getAndSetLock(user.id);

    return new AppResponseDto(
      CustomerResDto.forCustomer({
        data: customer,
        totalPoint: userPoint?.totalPoint,
      }),
    );
  }

  @Transactional()
  async updateProfile(user: User, dto: UpdateProfileCustomerReqDto) {
    let customer = user.customer;
    if (!customer) {
      customer = await this.customerRepo.findOneByOrThrowNotFoundExc({
        userId: user.id,
      });
    }

    await this.customerRepo.save({
      ...customer,
      ...dto,
    });

    return this.getProfile(user);
  }

  async updatePassword(user: User, body: UpdatePasswordCustomerReqDto) {
    const { password, newPassword } = body;

    const customer = await this.customerRepo.findOne({
      where: { userId: user.id },
    });

    if (!customer)
      throw new NotFoundExc({
        message: 'common.exc.notFound',
        params: { name: 'customer' },
      });
    if (!this.encryptService.compareHash(password, customer.password))
      throw new ExpectationFailedExc({
        message: 'auth.customer.wrongOldPassword',
      });

    await this.customerRepo.save({
      ...customer,
      password: this.encryptService.encryptText(newPassword),
    });
  }
}
