import { Injectable } from '@nestjs/common/decorators';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { Brackets } from 'typeorm';
import {
  BlockedHistoryAction,
  BlockedHistoryType,
} from '../../../blacklist/enums/blocked-history.enum';
import { BlockedHistoryRepository } from '../../../blacklist/repositories/blocked-history.repository';
import { GlobalConfig } from '../../../common/config/global.config';
import { ExceptionSubCode } from '../../../common/constants/exception.constant';
import { UnauthorizedExc } from '../../../common/exceptions/custom.exception';
import { Customer } from '../../entities/customer.entity';
import { JwtAuthPayload } from '../../interfaces/jwt-payload.interface';

@Injectable()
export class AuthCommonService {
  constructor(
    private configService: ConfigService<GlobalConfig>,
    private jwtService: JwtService,
    private blockedHistoryRepo: BlockedHistoryRepository,
  ) {}

  generateAccessToken(payload: JwtAuthPayload) {
    return this.jwtService.sign(payload, {
      expiresIn: this.configService.get('auth.accessToken.expiresTime'),
      secret: this.configService.get('auth.accessToken.secret'),
    });
  }

  generateRefreshToken(payload: JwtAuthPayload) {
    return this.jwtService.sign(payload, {
      expiresIn: this.configService.get('auth.refreshToken.expiresTime'),
      secret: this.configService.get('auth.refreshToken.secret'),
    });
  }

  async getLatestCustomerBlockedHistory(
    userId: number,
    type: BlockedHistoryType,
  ) {
    const customerBlockedHistory = await this.blockedHistoryRepo
      .createQueryBuilder('blockedHistory')
      .where('blockedHistory.userId = :userId', { userId })
      .andWhere('blockedHistory.type = :type', { type: type })
      .andWhere(
        new Brackets((qb2) => {
          qb2
            .where('blockedHistory.expireDate is null')
            .orWhere('blockedHistory.expireDate > now()');
        }),
      )
      .orderBy('blockedHistory.createdAt', 'DESC')
      .getOne();

    return customerBlockedHistory;
  }

  async checkBlockedAccountCustomer(
    customer: Customer,
    blockedAccount: boolean,
  ) {
    const customerBlockedHistory = await this.getLatestCustomerBlockedHistory(
      customer.userId,
      BlockedHistoryType.ACCOUNT,
    );

    if (
      blockedAccount &&
      customerBlockedHistory?.action === BlockedHistoryAction.BLOCK
    )
      throw new UnauthorizedExc({
        message: 'auth.customer.blockedAccount',
        subCode: ExceptionSubCode.ACCOUNT_WAS_BLOCKED,
      });

    if (
      !blockedAccount &&
      customerBlockedHistory?.action === BlockedHistoryAction.UN_BLOCK
    )
      throw new UnauthorizedExc({ message: 'auth.customer.unBlockAccount' });
  }

  async checkBlockedScanQrCodeCustomer(
    customer: Customer,
    lockedScanQrCode: boolean,
  ) {
    const customerBlockedHistory = await this.getLatestCustomerBlockedHistory(
      customer.userId,
      BlockedHistoryType.SCAN_QR_CODE,
    );

    if (
      lockedScanQrCode &&
      customerBlockedHistory?.action === BlockedHistoryAction.BLOCK
    )
      throw new UnauthorizedExc({ message: 'auth.customer.blockedScanQrCode' });

    if (
      !lockedScanQrCode &&
      customerBlockedHistory?.action === BlockedHistoryAction.UN_BLOCK
    )
      throw new UnauthorizedExc({
        message: 'auth.customer.unBlockedScanQrCode',
      });
    else return customerBlockedHistory;
  }
}
