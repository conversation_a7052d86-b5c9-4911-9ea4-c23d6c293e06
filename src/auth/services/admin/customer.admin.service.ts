import { Injectable } from '@nestjs/common';
import { paginate, Pagination } from 'nestjs-typeorm-paginate';
import { Transactional } from 'typeorm-transactional';
import { DeleteMultipleByIdNumberReqDto } from '../../../common/dtos/delete-multiple.dto';
import {
  ExpectationFailedExc,
  NotFoundExc,
} from '../../../common/exceptions/custom.exception';
import {
  GetListCustomerAdminReqDto,
  SearchBy,
} from '../../dtos/admin/req/customer.admin.req.dto';
import { CustomerResDto } from '../../dtos/common/res/customer.res.dto';
import { CustomerRepository } from '../../repositories/customer.repository';

@Injectable()
export class CustomerAdminService {
  constructor(private customerRepo: CustomerRepository) {}

  @Transactional()
  async getList(dto: GetListCustomerAdminReqDto) {
    const { searchBy, limit, page } = dto;
    let { searchText } = dto;
    const queryBuilder = this.customerRepo
      .createQueryBuilder('customer')
      .orderBy('customer.id');

    if (searchBy && searchText) {
      searchText = `%${dto.searchText}%`;

      switch (searchBy) {
        case SearchBy.EMAIL:
          queryBuilder.where('customer.email ILIKE :searchText', {
            searchText,
          });
          break;
        case SearchBy.MERCHANT_NAME:
          queryBuilder
            .innerJoin('customer.merchantUser', 'mu')
            .innerJoin('mu.merchant', 'merchant')
            .where('merchant.name ILIKE :searchText', { searchText });
          break;
        case SearchBy.PHONE:
          queryBuilder.where('customer.phoneNumber ILIKE :searchText', {
            searchText,
          });
          break;
      }
    }

    const { items, meta } = await paginate(queryBuilder, { limit, page });

    const customers = items.map((item) =>
      CustomerResDto.forAdmin({ data: item }),
    );
    return new Pagination(customers, meta);
  }

  async getDetail(id: number) {
    const customer = await this.customerRepo.findOneOrThrowNotFoundExc({
      where: { id },
      relations: {
        user: true,
        merchantUser: { merchant: { avatar: true } },
      },
    });

    return CustomerResDto.forAdmin({ data: customer });
  }

  async deleteMultiple(dto: DeleteMultipleByIdNumberReqDto) {
    const { ids } = dto;
    const { affected } = await this.customerRepo.softDelete(ids);

    if (affected !== ids.length)
      throw new ExpectationFailedExc({
        message: 'auth.common.deleteMultipleError',
      });
  }

  async deleteSingle(id: number) {
    const { affected } = await this.customerRepo.softDelete(id);

    if (!affected)
      throw new NotFoundExc({ message: 'auth.customer.customerNotFound' });
  }
}
