import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { EventName } from 'src/common/enums/event.enum';
import { Transactional } from 'typeorm-transactional';
import { GlobalConfig } from '../../../common/config/global.config';
import { ExceptionSubCode } from '../../../common/constants/exception.constant';
import {
  NotFoundExc,
  UnauthorizedExc,
} from '../../../common/exceptions/custom.exception';
import { DeviceType } from '../../../system-config/enums/app-config.enum';
import { AppConfigRepository } from '../../../system-config/repositories/app-config.repository';
import { EncryptService } from '../../../utils/services/encrypt.service';
import { RefreshTokenReqDto } from '../../dtos/common/req/auth.req.dto';
import { AuthTokenResDto } from '../../dtos/common/res/auth-token.res.dto';
import { MerchantResDto } from '../../dtos/common/res/merchant.res.dto';
import {
  MerchantLoginReqDto,
  RegisterMerchantReqDto,
} from '../../dtos/merchant/req/auth.merchant.req.dto';
import { User } from '../../entities/user.entity';
import { MerchantStatus } from '../../enums/merchant.enum';
import { UserType } from '../../enums/user.enum';
import { JwtAuthPayload } from '../../interfaces/jwt-payload.interface';
import { MerchantRepository } from '../../repositories/merchant.repository';
import { UserRepository } from '../../repositories/user.repository';
import { AuthCommonService } from '../common/auth.common.service';
@Injectable()
export class AuthMerchantService {
  constructor(
    private userRepo: UserRepository,
    private merchantRepo: MerchantRepository,
    private jwtService: JwtService,
    private encryptService: EncryptService,
    private authCommonService: AuthCommonService,
    private configService: ConfigService<GlobalConfig>,
    private eventEmitter: EventEmitter2,
    private appConfigRepo: AppConfigRepository,
  ) {}

  @Transactional()
  async register(dto: RegisterMerchantReqDto) {
    const { email, password } = dto;

    const existedMerchant = await this.merchantRepo.findOneBy({ email });
    if (existedMerchant)
      throw new NotFoundExc({ message: 'auth.merchant.merchantNotFound' });

    const user = await this.userRepo.save({ type: UserType.MERCHANT });

    const merchant = this.merchantRepo.create({
      email,
      password: this.encryptService.encryptText(password),
      user,
      status: MerchantStatus.UNVERIFIED,
    });

    await Promise.all([
      this.merchantRepo.save(merchant),
      this.eventEmitter.emitAsync(EventName.MERCHANT_REGISTERED, user.id),
      // this.handleSendVerification(user.id, email),

      // Create default home_config
      // We might use better solution
    ]);
    const appConfig = this.appConfigRepo.create([
      {
        mobileVersion: 1.0, // default mobile version
        deviceType: DeviceType.ANDROID,
        ownerId: merchant.id,
      },
      {
        mobileVersion: 1.0, // default mobile version
        deviceType: DeviceType.IOS,
        ownerId: merchant.id,
      },
    ]);
    this.appConfigRepo.save(appConfig);

    return MerchantResDto.forMerchant({ data: merchant });
  }

  async login(dto: MerchantLoginReqDto) {
    const { email, password } = dto;
    const merchant = await this.merchantRepo
      .createQueryBuilder('merchant')
      .addSelect('merchant.password')
      .innerJoinAndSelect('merchant.user', 'user')
      .where('merchant.email = :email', { email })
      .andWhere('merchant.status = :status', {
        status: MerchantStatus.APPROVED,
      })
      .getOne();

    if (!merchant)
      throw new NotFoundExc({ message: 'auth.merchant.merchantNotFound' });

    if (!bcrypt.compareSync(password, merchant.password))
      throw new UnauthorizedExc({ message: 'auth.common.wrongOldPassword' });

    const payload: JwtAuthPayload = { userId: merchant.userId };
    const accessToken = this.authCommonService.generateAccessToken(payload);
    const refreshToken = this.authCommonService.generateRefreshToken(payload);

    return AuthTokenResDto.forMerchant({ data: { accessToken, refreshToken } });
  }

  async getCurrent(user: User) {
    const merchant = await this.merchantRepo.findOneOrThrowNotFoundExc({
      where: { userId: user.id },
      relations: {
        avatar: true,
        parent: true,
        user: {
          userToGroupPolicies: {
            groupPolicy: { groupToPolicies: { policy: true } },
          },
        },
      },
    });

    return MerchantResDto.forMerchant({ data: merchant });
  }

  async refreshToken(dto: RefreshTokenReqDto) {
    const { refreshToken } = dto;

    try {
      const payload = this.jwtService.verify<JwtAuthPayload>(refreshToken, {
        secret: this.configService.get('auth.refreshToken.secret'),
      });
      const accessToken = this.authCommonService.generateAccessToken({
        userId: payload.userId,
      });

      return AuthTokenResDto.forMerchant({ data: { accessToken } });
    } catch (error) {
      throw new UnauthorizedExc({
        subCode: ExceptionSubCode.INVALID_REFRESH_TOKEN,
        message: 'common',
      });
    }
  }
}
