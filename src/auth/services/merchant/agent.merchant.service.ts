import { Injectable } from '@nestjs/common';
import { paginate, Pagination } from 'nestjs-typeorm-paginate';
import { In } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { UserToGroupPolicy } from '../../../casl/entities/user-to-group-policy.entity';
import { GroupPolicyType } from '../../../casl/enums/group-policy.enum';
import { GroupPolicyRepository } from '../../../casl/repositories/group-policy.repository';
import { UserToGroupPolicyRepository } from '../../../casl/repositories/user-to-group-policy.repository';
import { DeleteMultipleByIdNumberReqDto } from '../../../common/dtos/delete-multiple.dto';
import {
  ExpectationFailedExc,
  NotFoundExc,
} from '../../../common/exceptions/custom.exception';
import { EncryptService } from '../../../utils/services/encrypt.service';
import { MerchantResDto } from '../../dtos/common/res/merchant.res.dto';
import {
  CreateAgentMerchantReqDto,
  ListAgentMerchantReqDto,
  UpdateAgentMerchantReqDto,
} from '../../dtos/merchant/req/auth.merchant.req.dto';
import { User } from '../../entities/user.entity';
import { MerchantStatus } from '../../enums/merchant.enum';
import { UserType } from '../../enums/user.enum';
import { MerchantRepository } from '../../repositories/merchant.repository';
import { UserRepository } from '../../repositories/user.repository';

@Injectable()
export class AgentMerchantService {
  constructor(
    private userRepo: UserRepository,
    private merchantRepo: MerchantRepository,
    private encryptService: EncryptService,
    private groupPolicyRepo: GroupPolicyRepository,
    private userToGroupPolicyRepo: UserToGroupPolicyRepository,
  ) {}

  async getList(dto: ListAgentMerchantReqDto, user: User) {
    const { rank, status, limit, page } = dto;
    let { searchText } = dto;

    const rootMerchantId = await this.merchantRepo.getRootMerchantId(
      user.merchant,
    );
    const queryBuilder = this.merchantRepo
      .createQueryBuilder('merchant')
      .leftJoinAndSelect('merchant.avatar', 'avatar')
      .where('merchant.parentId = :rootMerchantId', { rootMerchantId })
      .orderBy('merchant.id');

    if (searchText) {
      searchText = `%${searchText}%`;
      queryBuilder.andWhere('merchant.email ILIKE :searchText', {
        searchText,
      });
    }
    if (status) queryBuilder.andWhere('merchant.status = :status', { status });
    if (rank) queryBuilder.andWhere('merchant.rank = :rank', { rank });

    const { items, meta } = await paginate(queryBuilder, { limit, page });

    const agents = items.map((item) =>
      MerchantResDto.forMerchant({ data: item }),
    );

    return new Pagination(agents, meta);
  }

  async getDetail(id: number, user: User) {
    const rootMerchantId = await this.merchantRepo.getRootMerchantId(
      user.merchant,
    );

    const agent = await this.merchantRepo.findOneOrThrowNotFoundExc({
      where: { id, parentId: rootMerchantId },
      relations: {
        avatar: true,
        user: { userToGroupPolicies: { groupPolicy: true } },
      },
    });

    return MerchantResDto.forMerchant({ data: agent });
  }

  @Transactional()
  async create(dto: CreateAgentMerchantReqDto, currentUser: User) {
    const { email, password, groupPolicyIds } = dto;
    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      currentUser.merchant,
    );
    const rootMerchantId = await this.merchantRepo.getRootMerchantId(
      currentUser.merchant,
    );

    const existedMerchant = await this.merchantRepo.findOneBy({ email });
    if (existedMerchant)
      throw new NotFoundExc({ message: 'auth.merchant.existed' });

    await this.checkGroupPolicies(groupPolicyIds, rootMerchantUserId);

    const user = await this.userRepo.save({ type: UserType.MERCHANT });

    const merchant = this.merchantRepo.create({
      email,
      password: this.encryptService.encryptText(password),
      user,
      status: MerchantStatus.APPROVED,
      parentId: rootMerchantId,
    });

    await Promise.all([
      this.merchantRepo.save(merchant),
      this.saveAgentGroupPolicies(groupPolicyIds, [], user.id),
    ]);

    return this.getDetail(merchant.id, currentUser);
  }

  @Transactional()
  async update(dto: UpdateAgentMerchantReqDto, user: User) {
    const { id, status, groupPolicyIds } = dto;
    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );
    const rootMerchantId = await this.merchantRepo.getRootMerchantId(
      user.merchant,
    );

    let agent = await this.merchantRepo.findOneOrThrowNotFoundExc({
      where: { id: id, parentId: rootMerchantId },
      relations: { user: { userToGroupPolicies: true } },
    });

    await this.checkGroupPolicies(groupPolicyIds, rootMerchantUserId);

    agent = { ...agent, status };

    await Promise.all([
      this.merchantRepo.save(agent),
      this.saveAgentGroupPolicies(
        groupPolicyIds,
        agent.user.userToGroupPolicies,
        agent.userId,
      ),
    ]);

    return this.getDetail(agent.id, user);
  }

  private async saveAgentGroupPolicies(
    groupPolicyIds: number[],
    userToGroupPolicies: UserToGroupPolicy[],
    userId: number,
  ) {
    const userToGroupPolicyIdsToRemove: number[] = [];
    const userToGroupPoliciesToInsert: UserToGroupPolicy[] = [];

    for (const userToGroupPolicy of userToGroupPolicies) {
      const isExisted = groupPolicyIds.includes(
        userToGroupPolicy.groupPolicyId,
      );
      if (!isExisted) userToGroupPolicyIdsToRemove.push(userToGroupPolicy.id);
    }

    for (const groupPolicyId of groupPolicyIds) {
      const isExisted = userToGroupPolicies.some(
        (item) => item.groupPolicyId === groupPolicyId,
      );
      if (isExisted) continue;

      userToGroupPoliciesToInsert.push(
        this.userToGroupPolicyRepo.create({
          groupPolicyId,
          userId,
        }),
      );
    }

    await Promise.all([
      userToGroupPolicyIdsToRemove?.length &&
        this.userToGroupPolicyRepo.softDelete(userToGroupPolicyIdsToRemove),
      userToGroupPoliciesToInsert?.length &&
        this.userToGroupPolicyRepo.insert(userToGroupPoliciesToInsert),
    ]);
  }

  private async checkGroupPolicies(
    groupPolicyIds: number[],
    rootMerchantUserId: number,
  ) {
    await Promise.all(
      groupPolicyIds.map(async (item) => {
        await this.groupPolicyRepo.findOneByOrThrowNotFoundExc([
          {
            id: item,
            ownerId: rootMerchantUserId,
            type: GroupPolicyType.MERCHANT,
          },
          { id: item, type: GroupPolicyType.COMMON },
        ]);
      }),
    );
  }

  async deleteSingle(id: number, user: User) {
    const rootMerchantId = await this.merchantRepo.getRootMerchantId(
      user.merchant,
    );

    const { affected } = await this.merchantRepo.softDelete({
      id,
      parentId: rootMerchantId,
    });

    if (!affected)
      throw new NotFoundExc({ message: 'auth.agent.agentNotFound' });
  }

  async deleteList(dto: DeleteMultipleByIdNumberReqDto, user: User) {
    const { ids } = dto;
    const rootMerchantId = await this.merchantRepo.getRootMerchantId(
      user.merchant,
    );

    const { affected } = await this.merchantRepo.softDelete({
      id: In(ids),
      parentId: rootMerchantId,
    });

    if (affected !== ids.length)
      throw new ExpectationFailedExc({
        message: 'auth.common.deleteMultipleError',
      });
  }
}
