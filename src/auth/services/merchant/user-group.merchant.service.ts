import { Injectable } from '@nestjs/common';
import { paginate, Pagination } from 'nestjs-typeorm-paginate';
import { In } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { DeleteMultipleByIdNumberReqDto } from '../../../common/dtos/delete-multiple.dto';
import {
  ExpectationFailedExc,
  NotFoundExc,
} from '../../../common/exceptions/custom.exception';
import { syncArrayPos } from '../../../common/utils';
import { UserGroupResDto } from '../../dtos/common/res/user-group.res.dto';
import {
  CreateGroupAllCustomerMerchantReqDto,
  CreateGroupCustomerMerchantReqDto,
  GetListCustomerMerchantReqDto,
  GetListGroupCustomerMerchantReqDto,
} from '../../dtos/merchant/req/customer.merchant.req.dto';
import { Customer } from '../../entities/customer.entity';
import { UserGroupToUser } from '../../entities/user-group-to-user.entity';
import { User } from '../../entities/user.entity';
import { CustomerRepository } from '../../repositories/customer.repository';
import { MerchantRepository } from '../../repositories/merchant.repository';
import { UserGroupToUserRepository } from '../../repositories/user-group-to-user.repository';
import { UserGroupRepository } from '../../repositories/user-group.repository';
import { CustomerMerchantService } from './customer.merchant.service';

@Injectable()
export class UserGroupMerchantService {
  constructor(
    private customerRepo: CustomerRepository,
    private merchantRepo: MerchantRepository,
    private userGroupToUserRepo: UserGroupToUserRepository,
    private userGroupRepo: UserGroupRepository,
    private customerMerchantService: CustomerMerchantService,
  ) {}

  @Transactional()
  async createGroupCustomer(
    dto: CreateGroupCustomerMerchantReqDto,
    user: User,
  ) {
    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );
    await this.checkAndGetListCustomer(dto.ids, rootMerchantUserId);

    const userGroup = this.userGroupRepo.create({
      ownerId: rootMerchantUserId,
      name: dto.name,
      description: dto.description,
      status: dto.status,
    });
    await this.userGroupRepo.save(userGroup);

    await Promise.all(
      dto.ids.map(async (item) => {
        const userGroupToUser = this.userGroupToUserRepo.create({
          userGroupId: userGroup.id,
          userId: item,
        });
        await this.userGroupToUserRepo.save(userGroupToUser);
        return userGroupToUser;
      }),
    );
    return UserGroupResDto.forMerchant({ data: userGroup });
  }

  @Transactional()
  async createGroupAllCustomer(
    dto: CreateGroupAllCustomerMerchantReqDto,
    user: User,
  ) {
    const { description, groupName, status, ...getCustomerDto } = dto;

    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );

    const userGroup = this.userGroupRepo.create({
      ownerId: rootMerchantUserId,
      name: groupName,
      description: description,
      status: status,
    });
    await this.userGroupRepo.save(userGroup);

    const batchSize = 500;

    const queryBuilder = this.customerMerchantService.getQueryBuilderGetList(
      getCustomerDto as GetListCustomerMerchantReqDto,
      rootMerchantUserId,
    );
    const [customers, totalRecords] = await queryBuilder.getManyAndCount();

    for (let i = 0; i < totalRecords / batchSize; i++) {
      const listUserGroupToUser: UserGroupToUser[] = [];
      let size = 0;
      if (i < Math.floor(totalRecords / batchSize)) {
        size = batchSize;
      } else {
        size = totalRecords - i * batchSize;
      }
      for (let j = 0; j < size; j++) {
        listUserGroupToUser.push(
          this.userGroupToUserRepo.create({
            userGroupId: userGroup.id,
            userId: customers[i * batchSize + j].userId,
          }),
        );
      }
      await this.userGroupToUserRepo.save(listUserGroupToUser);
    }
    return UserGroupResDto.forMerchant({ data: userGroup });
  }

  @Transactional()
  async updateGroupCustomer(
    dto: CreateGroupCustomerMerchantReqDto,
    user: User,
    id: number,
  ) {
    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );
    await this.checkAndGetListCustomer(dto.ids, rootMerchantUserId);

    let userGroup = await this.userGroupRepo.findOneOrThrowNotFoundExc({
      where: { id, ownerId: rootMerchantUserId },
      relations: { userGroupToUsers: true },
    });

    userGroup = this.userGroupRepo.create({
      ...userGroup,
      name: dto.name,
      description: dto.description,
      status: dto.status,
    });
    await this.userGroupRepo.save(userGroup);
    await this.saveUserGroup(userGroup.userGroupToUsers, dto.ids, userGroup.id);
    return UserGroupResDto.forMerchant({ data: userGroup });
  }

  private async saveUserGroup(
    entities: UserGroupToUser[],
    dtos: number[],
    userGroupId: number,
  ) {
    const entitiesToSave: UserGroupToUser[] = [];
    const entityIdsToRemove: number[] = [];

    for (const entity of entities) {
      const dto = dtos.some((userId) => userId === entity.userId);
      if (!dto) entityIdsToRemove.push(entity.userId);
    }

    for (const dto of dtos) {
      let entity = entities.find((item) => dto === item.userId);

      if (!entity) {
        entity = this.userGroupToUserRepo.create({
          userGroupId: userGroupId,
          userId: dto,
        });
        entitiesToSave.push(entity);
      }
    }

    await Promise.all([
      entityIdsToRemove.length &&
        this.userGroupToUserRepo.softDelete({
          userId: In(entityIdsToRemove),
        }),
      entitiesToSave.length && this.userGroupToUserRepo.insert(entitiesToSave),
    ]);
  }

  private async saveAllUserGroup(
    entities: UserGroupToUser[],
    dtos: Customer[],
    userGroupId: number,
  ) {
    const entitiesToSave: UserGroupToUser[] = [];
    const entityIdsToRemove: number[] = [];

    for (const entity of entities) {
      const dto = dtos.some((customer) => customer.userId === entity.userId);
      if (!dto) entityIdsToRemove.push(entity.userId);
    }

    for (const dto of dtos) {
      let entity = entities.find((item) => dto.userId === item.userId);

      if (!entity) {
        entity = this.userGroupToUserRepo.create({
          userGroupId: userGroupId,
          userId: dto.userId,
        });
        entitiesToSave.push(entity);
      }
    }

    const batchSize = 500;

    for (let i = 0; i < entityIdsToRemove.length / batchSize; i++) {
      await this.userGroupToUserRepo.softDelete({
        userId: In(
          entityIdsToRemove.slice(i * batchSize, i * batchSize + batchSize),
        ),
      });
    }

    for (let i = 0; i < entitiesToSave.length / batchSize; i++) {
      await this.userGroupToUserRepo.insert(
        entitiesToSave.slice(i * batchSize, i * batchSize + batchSize),
      );
    }

    // await Promise.all([
    //   entityIdsToRemove.length &&
    //     this.userGroupToUserRepo.softDelete({
    //       userId: In(entityIdsToRemove),
    //     }),
    //   entitiesToSave.length && this.userGroupToUserRepo.insert(entitiesToSave),
    // ]);
  }

  @Transactional()
  async updateGroupCustomerAll(
    dto: CreateGroupAllCustomerMerchantReqDto,
    user: User,
    id: number,
  ) {
    const { description, groupName, status, ...getCustomerDto } = dto;
    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );

    let userGroup = await this.userGroupRepo.findOneOrThrowNotFoundExc({
      where: { id, ownerId: rootMerchantUserId },
      relations: { userGroupToUsers: true },
    });

    userGroup = this.userGroupRepo.create({
      ...userGroup,
      name: groupName,
      description: description,
      status: status,
    });
    await this.userGroupRepo.save(userGroup);

    const queryBuilder = this.customerMerchantService.getQueryBuilderGetList(
      getCustomerDto as GetListCustomerMerchantReqDto,
      rootMerchantUserId,
    );

    const customers = await queryBuilder.getMany();

    await this.saveAllUserGroup(
      userGroup.userGroupToUsers,
      customers,
      userGroup.id,
    );
    return UserGroupResDto.forMerchant({ data: userGroup });
  }

  private async checkAndGetListCustomer(
    ids: number[],
    rootMerchantUserId: number,
  ) {
    const listUsers = await this.customerRepo.findBy({
      userId: In(ids),
      merchantUserId: rootMerchantUserId,
    });

    if (listUsers.length !== ids.length)
      throw new ExpectationFailedExc({ message: 'auth.common.invalidUser' });
    return listUsers;
  }

  @Transactional()
  async getListGroupCustomer(
    dto: GetListGroupCustomerMerchantReqDto,
    user: User,
  ) {
    const { limit, page } = dto;
    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );
    const queryBuilder = this.userGroupRepo
      .createQueryBuilder('ug')
      .orderBy('ug.id', 'DESC')
      .where('ug.ownerId = :ownerId', {
        ownerId: rootMerchantUserId,
      });

    const { items, meta } = await paginate(queryBuilder, { limit, page });

    const userGroups = await this.userGroupRepo.find({
      where: { id: In(items.map((item) => item.id)) },
      relations: { userGroupToUsers: true },
    });
    syncArrayPos(items, userGroups);

    const result = userGroups.map((item) =>
      UserGroupResDto.forMerchant({ data: item }),
    );

    return new Pagination(result, meta);
  }

  async getGroupCustomerDetail(id: number, user: User) {
    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );
    const userGroup = await this.userGroupRepo.findOneOrThrowNotFoundExc({
      where: { id, ownerId: rootMerchantUserId },
      relations: { userGroupToUsers: true },
    });

    return UserGroupResDto.forMerchant({ data: userGroup });
  }

  @Transactional()
  async deleteSingle(id: number, user: User) {
    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );
    const { affected } = await this.userGroupRepo.softDelete({
      id,
      ownerId: rootMerchantUserId,
    });
    if (!affected)
      throw new NotFoundExc({ message: 'auth.userGroup.userGroupNotFound' });
  }

  @Transactional()
  async deleteMultiple(dto: DeleteMultipleByIdNumberReqDto, user: User) {
    const { ids } = dto;
    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );
    const { affected } = await this.userGroupRepo.softDelete({
      id: In(ids),
      ownerId: rootMerchantUserId,
    });

    if (affected !== ids.length)
      throw new ExpectationFailedExc({
        message: 'auth.userGroup.deleteMultipleError',
      });
  }
}
