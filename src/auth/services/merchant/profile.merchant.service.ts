import { Injectable } from '@nestjs/common';
import { ExpectationFailedExc } from '../../../common/exceptions/custom.exception';
import { EncryptService } from '../../../utils/services/encrypt.service';
import { MerchantResDto } from '../../dtos/common/res/merchant.res.dto';
import {
  ChangePasswordMerchantReqDto,
  UpdateProfileMerchantReqDto,
} from '../../dtos/merchant/req/profile.merchant.req.dto';
import { User } from '../../entities/user.entity';
import { MerchantRepository } from '../../repositories/merchant.repository';

@Injectable()
export class ProfileMerchantService {
  constructor(
    private merchantRepo: MerchantRepository,
    private encryptService: EncryptService,
  ) {}

  async get(user: User) {
    const merchant = await this.merchantRepo.findOneOrThrowNotFoundExc({
      where: { userId: user.id },
      relations: { avatar: true },
    });

    return MerchantResDto.forMerchant({ data: merchant });
  }

  async update(dto: UpdateProfileMerchantReqDto, user: User) {
    const { address, avatarId, name, phoneNumber } = dto;

    let merchant = await this.merchantRepo.findOneByOrThrowNotFoundExc({
      userId: user.id,
    });

    merchant = {
      ...merchant,
      address,
      name,
      phoneNumber,
      avatarId,
    };

    await this.merchantRepo.save(merchant);

    return this.get(user);
  }

  async changePassword(dto: ChangePasswordMerchantReqDto, user: User) {
    const { newPassword, password } = dto;

    const merchant = await this.merchantRepo.findOneOrThrowNotFoundExc({
      where: { userId: user.id },
      select: { password: true, id: true },
    });

    if (!this.encryptService.compareHash(password, merchant.password))
      throw new ExpectationFailedExc({
        message: 'auth.common.wrongOldPassword',
      });

    await this.merchantRepo.update(merchant.id, {
      password: this.encryptService.encryptText(newPassword),
    });
  }
}
