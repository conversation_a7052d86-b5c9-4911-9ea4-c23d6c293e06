import { Injectable, Logger } from '@nestjs/common';
import * as dayjs from 'dayjs';
import { paginate, Pagination } from 'nestjs-typeorm-paginate';
import { Transactional } from 'typeorm-transactional';
import xlsx from 'xlsx';
import {
  BlockedHistoryAction,
  BlockedHistoryType,
  BlockedReason,
} from '../../../blacklist/enums/blocked-history.enum';
import { BlockedHistoryRepository } from '../../../blacklist/repositories/blocked-history.repository';
import { AppResponseDto } from '../../../common/dtos/app-response.dto';
import { SupportFileType } from '../../../common/enums/file.enum';
import { NotFoundExc } from '../../../common/exceptions/custom.exception';
import { FileService } from '../../../file/file.service';
import { FileRepository } from '../../../file/repositories/file.repository';
import { CustomerResDto } from '../../dtos/common/res/customer.res.dto';
import {
  BlockCustomerAccountReqDto,
  BlockCustomerScanQrCodeReqDto,
  GetListCustomerMerchantReqDto,
  UpdateCustomerMerchantReqDto,
} from '../../dtos/merchant/req/customer.merchant.req.dto';
import { ExportCustomerMerchantResDto } from '../../dtos/merchant/res/customer.merchant.res.dto';
import { Customer } from '../../entities/customer.entity';
import { User } from '../../entities/user.entity';
import { CustomerRepository } from '../../repositories/customer.repository';
import { MerchantRepository } from '../../repositories/merchant.repository';
import { AuthCommonService } from '../common/auth.common.service';

@Injectable()
export class CustomerMerchantService {
  private logger = new Logger(CustomerMerchantService.name);
  constructor(
    private authCommonService: AuthCommonService,
    private customerRepo: CustomerRepository,
    private merchantRepo: MerchantRepository,
    private fileService: FileService,
    private fileRepo: FileRepository,
    private blockedHistoryRepo: BlockedHistoryRepository,
  ) {}

  @Transactional()
  async getListCustomer(dto: GetListCustomerMerchantReqDto, user: User) {
    const { limit, page } = dto;
    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );

    const queryBuilder = this.getQueryBuilderGetList(dto, rootMerchantUserId);
    const { items, meta } = await paginate(queryBuilder, { limit, page });

    const customers = await Promise.all(
      items.map(async (item) => {
        const blockAccount = await this.getAccountStatus(item);
        const blockAddCode = await this.getLockAddCodeStatus(item);
        return CustomerResDto.forMerchant({
          data: item,
          blockAccount,
          blockAddCode,
        });
      }),
    );

    return new Pagination(customers, meta);
  }

  @Transactional()
  async getDetailCustomer(id: number, user: User) {
    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );
    const customer = await this.customerRepo.findOneOrThrowNotFoundExc({
      where: { id, merchantUserId: rootMerchantUserId },
    });

    return CustomerResDto.forMerchant({
      data: customer,
    });
  }

  @Transactional()
  async requestExport(dto: GetListCustomerMerchantReqDto, user: User) {
    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );

    // const requestExport = this.requestExportRepo.create({
    //   status: RequestExportStatus.WAITING,
    //   resource: Resource.CUSTOMER,
    //   type: RequestExportType.MERCHANT,
    //   ownerId: rootMerchantUserId,
    //   fileName: `Export-Customer_${dayjs().format('HH:mm_DD-MM-YYYY')}`,
    //   params: dto,
    // });
    // await this.requestExportRepo.save(requestExport);

    // await this.exportQueue.add({
    //   requestExportId: requestExport.id,
    // });

    // return RequestExportResDto.forMerchant({ data: requestExport });
  }

  @Transactional()
  async handleExport(
    dto: GetListCustomerMerchantReqDto,
    user: User,
    fileName: string,
  ) {
    this.logger.log(`handler step 1`);
    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );
    this.logger.log(`handler step 2`);
    const queryBuilder = this.getQueryBuilderGetList(dto, rootMerchantUserId);
    const customers = await queryBuilder.getMany();
    this.logger.log(`handler step 3`);

    const sheetLimit = 1000;
    const workbook = xlsx.utils.book_new();

    if (!customers.length) {
      const worksheet = xlsx.utils.json_to_sheet([]);
      xlsx.utils.book_append_sheet(workbook, worksheet);
    }
    this.logger.log(`handler step 4`);

    for (let i = 0; i < Math.ceil(customers.length / sheetLimit); i++) {
      this.logger.log(`handler step 4, index: ${i}`);
      const listData = customers.slice(i * sheetLimit, (i + 1) * sheetLimit);
      const list = listData.map((item, index) =>
        ExportCustomerMerchantResDto.fromCustomer({
          data: item,
          stt: index + 1,
        }),
      );
      const worksheet = xlsx.utils.json_to_sheet(list);
      xlsx.utils.book_append_sheet(workbook, worksheet, `${i + 1}`, true);
    }
    this.logger.log(`handler step 5`);

    const key = await this.fileService.uploadFile(
      xlsx.write(workbook, { type: 'buffer', bookType: 'xlsx' }),
      SupportFileType.xlsx,
      rootMerchantUserId,
      fileName,
    );
    this.logger.log(`handler step 6`);

    const file = this.fileRepo.create({
      key,
      type: SupportFileType.xlsx,
      uploaderId: rootMerchantUserId,
    });
    await this.fileRepo.save(file);
    this.logger.log(`handler step 7`);

    return file;
  }

  getQueryBuilderGetList(
    dto: GetListCustomerMerchantReqDto,
    rootMerchantUserId: number,
  ) {
    const { name, phoneNumber, email } = dto;

    const queryBuilder = this.customerRepo
      .createQueryBuilder('customer')
      .leftJoinAndSelect('customer.user', 'user')
      .where('customer.merchantUserId = :rootMerchantUserId', {
        rootMerchantUserId,
      })
      .andWhere(`"customer"."deleted_at" IS NULL`)
      .orderBy('customer.id');

    if (name)
      queryBuilder.andWhere('customer.name ILIKE :name', {
        name: `%${name}%`,
      });

    if (phoneNumber)
      queryBuilder.andWhere('customer.phoneNumber ILIKE :phoneNumber', {
        phoneNumber: `%${phoneNumber}%`,
      });

    if (email)
      queryBuilder.andWhere('customer.email ILIKE :email', {
        email: `%${email}%`,
      });

    return queryBuilder;
  }

  @Transactional()
  async updateCustomerInfo(
    id: number,
    user: User,
    dto: UpdateCustomerMerchantReqDto,
  ) {
    const { email, name, address, birthDate, gender } = dto;

    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );
    let customer = await this.customerRepo.findOne({
      where: { id, merchantUserId: rootMerchantUserId },
    });

    if (!customer)
      throw new NotFoundExc({ message: 'auth.customer.customerNotFound' });

    customer = {
      ...customer,
      email: email ?? null,
      name: name ?? null,
      address: address ?? null,
      birthDate: birthDate ?? null,
      gender: gender ?? null,
    };
    console.log(dto);
    await this.customerRepo.save(customer);

    return this.getDetailCustomer(id, user);
  }

  async blockCustomerAccount(
    user: User,
    id: number,
    dto: BlockCustomerAccountReqDto,
  ) {
    const { blockAccount } = dto;
    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );

    const customer = await this.customerRepo.findOne({
      where: { id, merchantUserId: rootMerchantUserId },
    });
    if (!customer)
      throw new NotFoundExc({ message: 'auth.customer.customerNotFound' });

    if (blockAccount != null)
      await this.authCommonService.checkBlockedAccountCustomer(
        customer,
        blockAccount,
      );

    const blockedHistory = this.blockedHistoryRepo.create({
      userId: customer.userId,
      type: BlockedHistoryType.ACCOUNT,
      reason: blockAccount
        ? BlockedReason.ADMIN_BLOCK_ACCOUNT
        : BlockedReason.ADMIN_UN_BLOCK_ACCOUNT,
      action: blockAccount
        ? BlockedHistoryAction.BLOCK
        : BlockedHistoryAction.UN_BLOCK,
    });
    await this.blockedHistoryRepo.save(blockedHistory);

    return new AppResponseDto('ok');
  }

  async blockCustomerScanQrCode(
    user: User,
    id: number,
    dto: BlockCustomerScanQrCodeReqDto,
  ) {
    const { blockScanQrCode } = dto;
    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );

    const customer = await this.customerRepo.findOne({
      where: { id, merchantUserId: rootMerchantUserId },
    });

    if (!customer)
      throw new NotFoundExc({ message: 'auth.customer.customerNotFound' });

    if (blockScanQrCode != null)
      await this.authCommonService.checkBlockedScanQrCodeCustomer(
        customer,
        blockScanQrCode,
      );

    const blockedHistory = this.blockedHistoryRepo.create({
      userId: customer.userId,
      type: BlockedHistoryType.SCAN_QR_CODE,
      reason: blockScanQrCode
        ? BlockedReason.ADMIN_BLOCK_SCAN_QR_CODE
        : BlockedReason.ADMIN_UN_BLOCK_SCAN_QR_CODE,
      action: blockScanQrCode
        ? BlockedHistoryAction.BLOCK
        : BlockedHistoryAction.UN_BLOCK,
    });

    await this.blockedHistoryRepo.save(blockedHistory);

    return new AppResponseDto('ok');
  }

  private async getAccountStatus(customer: Customer) {
    const customerBlockedAccountHistory =
      await this.authCommonService.getLatestCustomerBlockedHistory(
        customer.userId,
        BlockedHistoryType.ACCOUNT,
      );

    if (customerBlockedAccountHistory?.action === BlockedHistoryAction.BLOCK)
      return true;
    else return false;
  }

  private async getLockAddCodeStatus(customer: Customer) {
    const customerBlockedPointHistory =
      await this.authCommonService.getLatestCustomerBlockedHistory(
        customer.userId,
        BlockedHistoryType.SCAN_QR_CODE,
      );

    if (
      customerBlockedPointHistory &&
      customerBlockedPointHistory?.action === BlockedHistoryAction.BLOCK &&
      (!customerBlockedPointHistory.expireDate ||
        dayjs(customerBlockedPointHistory.expireDate).isAfter(dayjs()))
    )
      return true;
    else return false;
  }
}
