import { RedisModule } from '@liaoliaots/nestjs-redis';
import { BullModule } from '@nestjs/bull';
import { Module, OnModuleInit, ValidationPipe } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_FILTER, APP_PIPE, ModuleRef } from '@nestjs/core';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';
import { ThrottlerModule } from '@nestjs/throttler';
import { TypeOrmModule } from '@nestjs/typeorm';
import * as dayjs from 'dayjs';
import * as customParseFormat from 'dayjs/plugin/customParseFormat';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';
import {
  AcceptLanguageResolver,
  HeaderResolver,
  I18nModule,
  QueryResolver,
} from 'nestjs-i18n';
import * as path from 'path';
import {
  addTransactionalDataSource,
  initializeTransactionalContext,
} from 'typeorm-transactional';
import { dataSource } from '../data-source';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { CartModule } from './cart/cart.module';
import { CaslModule } from './casl/casl.module';
import { CaslTaskService } from './casl/services/task/casl.task.service';
import { CategoryModule } from './category/category.module';
import { bullConfig } from './common/config/bull.config';
import globalConfig, { GlobalConfig } from './common/config/global.config';
import { redisConfig } from './common/config/redis.config';
import { TIME_ZONE } from './common/constants/global.constant';
import { AppEnvironment } from './common/enums/app.enum';
import { AllExceptionsFilter } from './common/filters/all.filter';
import { ExportModule } from './export/export.module';
import { ExternalModule } from './external/external.module';
import { FileRequestModule } from './file-request/file-request.module';
import { FileModule } from './file/file.module';
import { NotificationModule } from './notification/notification.module';
import { OrderModule } from './order/order.module';
import { PointModule } from './point/point.module';
import { ProductModule } from './product/product.module';
import { QrCodeModule } from './qr-code/qr-code.module';
import { TaskModule } from './task/task.module';
import { UtilsModule } from './utils/utils.module';

@Module({
  imports: [
    BullModule.forRootAsync(bullConfig),
    RedisModule.forRootAsync(redisConfig),
    EventEmitterModule.forRoot({
      maxListeners: 20,
    }),
    ConfigModule.forRoot({
      isGlobal: true,
      load: [() => globalConfig],
      cache: true,
    }),
    TypeOrmModule.forRootAsync({
      useFactory: () => ({}),
      dataSourceFactory: async () => {
        initializeTransactionalContext();
        return addTransactionalDataSource(dataSource);
      },
    }),
    I18nModule.forRoot({
      fallbackLanguage: 'vi',
      loaderOptions: { path: path.join(__dirname, 'i18n'), watch: true },
      resolvers: [
        new QueryResolver(['lang', 'l']),
        new HeaderResolver(['lang', 'l']),
        AcceptLanguageResolver,
      ],
      typesOutputPath: path.join(process.cwd(), '/src/i18n/i18n.generated.ts'),
    }),
    ThrottlerModule.forRoot([{ ttl: 60, limit: 10 }]),
    ScheduleModule.forRoot(),
    TaskModule,
    ExternalModule,
    UtilsModule,
    AuthModule,
    FileModule,
    CaslModule,
    CategoryModule,
    ProductModule,
    FileRequestModule,
    QrCodeModule,
    PointModule,
    CartModule,
    OrderModule,
    ExportModule,
    NotificationModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_PIPE,
      useValue: new ValidationPipe({
        transform: true,
        transformOptions: { exposeDefaultValues: true },
      }),
    },
    { provide: APP_FILTER, useClass: AllExceptionsFilter },
  ],
})
export class AppModule implements OnModuleInit {
  constructor(
    private configService: ConfigService<GlobalConfig>,
    private moduleRef: ModuleRef,
  ) {}

  async onModuleInit() {
    await dataSource.query('CREATE extension IF NOT EXISTS pgcrypto');
    dayjs.extend(utc);
    dayjs.extend(timezone);
    dayjs.tz.setDefault(TIME_ZONE);
    dayjs.extend(customParseFormat);

    const isLocalOrTest = [AppEnvironment.LOCAL, AppEnvironment.TEST].includes(
      this.configService.get('environment'),
    );

    if (isLocalOrTest) return;

    this.moduleRef.get(CaslTaskService, { strict: false }).syncPolicies();
  }
}
