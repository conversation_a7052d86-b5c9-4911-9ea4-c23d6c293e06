export enum EventName {
  CACHE_STALED = 'cache.staled',
  NOTI_PUSHED = 'noti.pushed',
  MERCHANT_REGISTERED = 'merchant.registered',
  CUSTOMER_SCANNED = 'customer.scanned',
  ZALO_NOTI_ADDPOINT = 'zalo.noti.addpoint',
  ZALO_NOTI_ORDER = 'zalo.noti.order',
  USER_REFUND_POINT = 'user.refundpoint',
  USER_ORDER_STATUS = 'user.order.status',
  USER_UPDATE_RANK = 'user.update.rank',
  SEND_GRID_EMAIL = 'sendgrid.email',
  MERCHANT_REJECT_REQUEST_REFUND = 'merchant.rejected.refund.request',
  CUSTOMER_DELETED = 'customer.deleted',
  CUSTOMER_EVENT_POINT = 'customer.event.point',

  CUSTOMER_SCAN_QR_TO_POINT = 'customer.scan.qr.to.point',
  CUSTOMER_SCAN_QR_TO_GIFT = 'customer.scan.qr.to.gift',
  CUSTOMER_EXCHANGE_GIFT = 'customer.exchange.gift',

  ORDER_E_VOUCHER_CHECK_STATUS = 'orderEVoucher.check.status',
}
