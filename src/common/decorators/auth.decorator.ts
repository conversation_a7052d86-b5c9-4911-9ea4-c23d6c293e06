import {
  applyDecorators,
  createParamDecorator,
  ExecutionContext,
  SetMetadata,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiHeader } from '@nestjs/swagger';
import { FastifyRequest } from 'fastify';
import { JwtAuthenAdminGuard } from '../../auth/guards/jwt-authen.admin.guard';
import { JwtAuthenCustomerGuard } from '../../auth/guards/jwt-authen.customer.guard';
import { JwtAuthenMerchantGuard } from '../../auth/guards/jwt-authen.merchant.guard';
import { JwtAuthenUserGuard } from '../../auth/guards/jwt-authen.user.guard';
import { JwtAbilityAdminGuard } from '../../casl/guard/ability.admin.guard';
import { JwtAbilityMerchantGuard } from '../../casl/guard/ability.merchant.guard';
import { ABILITY_METADATA_KEY } from '../constants/global.constant';
import { BadRequestExc } from '../exceptions/custom.exception';
import { RequiredRule } from '../interfaces/casl.interface';

export const IS_PUBLIC_KEY = Symbol();
export const Public = () => SetMetadata(IS_PUBLIC_KEY, true);

export const AuthenticateMerchant = () =>
  applyDecorators(UseGuards(JwtAuthenMerchantGuard), ApiBearerAuth());

export const AuthenticateCustomer = () =>
  applyDecorators(UseGuards(JwtAuthenCustomerGuard), ApiBearerAuth());

export const AuthenticateAdmin = () =>
  applyDecorators(UseGuards(JwtAuthenAdminGuard), ApiBearerAuth());

export const AuthenticateUser = () =>
  applyDecorators(UseGuards(JwtAuthenUserGuard), ApiBearerAuth());

export const AuthorizeAdmin = (...requirements: RequiredRule[]) => {
  return applyDecorators(
    UseGuards(JwtAbilityAdminGuard),
    SetMetadata(ABILITY_METADATA_KEY, requirements),
    ApiBearerAuth(),
  );
};

export const AuthorizeMerchant = (...requirements: RequiredRule[]) => {
  return applyDecorators(
    UseGuards(JwtAbilityMerchantGuard),
    SetMetadata(ABILITY_METADATA_KEY, requirements),
    ApiBearerAuth(),
  );
};

export const CurrentAuthData = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    return request.user;
  },
);

export function MerchantIdHeader() {
  return applyDecorators(ApiHeader({ name: 'merchant_id' }));
}

// Use snake case because header get automatically convert to lowercase
export const MerchantId = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest<FastifyRequest>();
    if (
      !request.headers?.merchant_id ||
      Number(request.headers.merchant_id) <= 0
    )
      throw new BadRequestExc({ message: 'common.validationError.merchantId' });

    return request.headers.merchant_id;
  },
);
