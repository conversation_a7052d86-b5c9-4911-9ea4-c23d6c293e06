import { DEFAULT_REDIS, RedisService } from '@liaoliaots/nestjs-redis';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ModuleRef } from '@nestjs/core';
import { OnEvent } from '@nestjs/event-emitter';
import { Redis } from 'ioredis';
import { IsNull } from 'typeorm';
import { MerchantStatus } from '../../auth/enums/merchant.enum';
import { MerchantRepository } from '../../auth/repositories/merchant.repository';
import { GlobalConfig } from '../../common/config/global.config';
import { REDIS_KEY } from '../../common/constants/redis.constant';
import { AppEnvironment } from '../../common/enums/app.enum';
import { EventName } from '../../common/enums/event.enum';
import { chunk, tryParseJson } from '../../common/utils';
import { HomeConfigCustomerService } from '../../system-config/services/customer/home-config.customer.service';
import { ShareAppConfigCustomerService } from '../../system-config/services/customer/share-app-config.customer.service';
import { UtilService } from '../../utils/services/util.service';
import { CacheData } from '../data/cache.data';
import {
  CacheOptions,
  ReCacheIntDto,
  SaveCacheIntDto,
} from '../dtos/internal/cache.int.dto';
import { GetCacheReqDto } from '../dtos/req/cache.req.dto';
import { CacheKey } from '../enums/cache.enum';
import { CacheResponse } from '../interfaces/cache.interface';

@Injectable()
export class CacheService {
  private logger = new Logger(CacheService.name);
  private readonly redis: Redis | null;

  constructor(
    private moduleRef: ModuleRef,
    private readonly redisService: RedisService,
    private configService: ConfigService<GlobalConfig>,
    private utilService: UtilService,
    private merchantRepo: MerchantRepository,
  ) {
    this.redis = this.redisService.getOrThrow(DEFAULT_REDIS);
  }

  async save(dto: SaveCacheIntDto) {
    const { data, key, merchantId, options } = dto;
    const hash = this.calHashKey(key, merchantId, options);
    const cacheData: CacheData = { data, hash };

    try {
      await this.redis.hset(REDIS_KEY.CACHE, hash, JSON.stringify(cacheData));
    } catch (error) {
      this.logger.warn(`Redis hset failed: ${hash}`, error);
    }

    return cacheData;
  }

  async getRedisData<T = CacheResponse>(key: string): Promise<T | null> {
    try {
      const dataJson = await this.redis.hget(REDIS_KEY.CACHE, key);
      return tryParseJson(dataJson);
    } catch (error) {
      this.logger.warn(`Redis hget failed: ${key}`, error);
      return null;
    }
  }

  async remove({
    key,
    merchantId,
  }: Pick<SaveCacheIntDto, 'key' | 'merchantId'>) {
    try {
      const keys = await this.redis.hkeys(REDIS_KEY.CACHE);
      const toDelete = keys.filter((k) => k.startsWith(`${key}:${merchantId}`));
      if (toDelete.length > 0) {
        await this.redis.hdel(REDIS_KEY.CACHE, ...toDelete);
      }
    } catch (error) {
      this.logger.warn(`Redis hdel (bulk) failed for key=${key}`, error);
    }
  }

  async removeOne(
    dto: Pick<SaveCacheIntDto, 'key' | 'merchantId' | 'options'>,
  ) {
    const hash = this.calHashKey(dto.key, dto.merchantId, dto.options);
    try {
      await this.redis.hdel(REDIS_KEY.CACHE, hash);
    } catch (error) {
      this.logger.warn(`Redis hdel failed: ${hash}`, error);
    }
  }

  async removeByKey(key: CacheKey) {
    try {
      const hashKeys = await this.redis.hkeys(REDIS_KEY.CACHE);
      const chunks = chunk(
        hashKeys.filter((k) => k.includes(key)),
        20,
      );
      for (const group of chunks) {
        await Promise.all(
          group.map((hash) => this.redis.hdel(REDIS_KEY.CACHE, hash)),
        );
      }
    } catch (error) {
      this.logger.warn(`Redis removeByKey failed for key=${key}`, error);
    }
  }

  async getHash(dto: GetCacheReqDto, merchantId?: number) {
    const hash = this.calHashKey(dto.key, merchantId, dto.options);
    const data = await this.getRedisData<CacheResponse>(hash);
    return data?.hash;
  }

  async getData(dto: GetCacheReqDto, merchantId?: number) {
    const hash = this.calHashKey(dto.key, merchantId, dto.options);
    const data = await this.getRedisData<CacheResponse>(hash);
    return data ? { ...data.data, hash: data.hash } : undefined;
  }

  async initCache() {
    try {
      const merchants = await this.merchantRepo.findBy({
        status: MerchantStatus.APPROVED,
        parentId: IsNull(),
      });

      for (const cacheKey of Object.values(CacheKey)) {
        for (const merchant of merchants) {
          await this.reCache({ key: cacheKey, merchantId: merchant.id });
        }
      }
    } catch (error) {
      this.logger.error('Failed to initialize cache', error);
    }
  }

  @OnEvent(EventName.CACHE_STALED)
  async reCache(dto: Pick<SaveCacheIntDto, 'key' | 'merchantId' | 'options'>) {
    if (process.env.NODE_ENV === AppEnvironment.TEST) return;

    try {
      switch (dto.key) {
        case CacheKey.HOME_CONFIG:
          await this.reCacheHomeConfig(dto);
          break;
        case CacheKey.SHARE_APP_CONFIG:
          await this.reCacheShareApp(dto);
          break;
        default:
          throw new Error(`Unrecognized CacheKey: ${dto.key}`);
      }
    } catch (error) {
      this.logger.error(`Failed to reCache: ${JSON.stringify(dto)}`, error);
      try {
        await this.removeOne(dto);
      } catch (fallbackError) {
        this.logger.error(
          `Fallback removeOne failed: ${JSON.stringify(dto)}`,
          fallbackError,
        );
        await this.remove({ key: dto.key, merchantId: dto.merchantId });
      }
    }
  }

  private calHashKey(
    key: CacheKey,
    merchantId?: number,
    options: CacheOptions = {},
  ): string {
    const query = options.query || {};
    const params = options.params || {};

    const signature = {
      query: {
        page: query.page?.toString() ?? null,
        limit: query.limit?.toString() ?? null,
        categoryIds: query.categoryIds?.toString() ?? null,
      },
      params: {
        id: params.id?.toString() ?? null,
      },
    };

    return `${key}:${merchantId}:${JSON.stringify(signature)}`;
  }

  private async reCacheHomeConfig(dto: ReCacheIntDto) {
    const service = await this.utilService.getService(
      HomeConfigCustomerService,
    );
    const data = await service.get(dto.merchantId);
    await this.save({ ...dto, data });
  }

  private async reCacheShareApp(dto: ReCacheIntDto) {
    const service = await this.utilService.getService(
      ShareAppConfigCustomerService,
    );
    const data = await service.get(dto.merchantId);
    await this.save({ ...dto, data });
  }
}
