import { PickType } from '@nestjs/swagger';
import { NonFunctionProperties } from '../../../common/types/utils.type';
import { CacheKey } from '../../enums/cache.enum';

export class SaveCacheIntDto {
  key: CacheKey;
  merchantId?: number;
  data: any;
  options?: CacheOptions;

  constructor(commandData: NonFunctionProperties<SaveCacheIntDto>) {
    this.data = commandData.data;
    this.merchantId = commandData.merchantId;
    this.key = commandData.key;
    this.options = commandData?.options;
  }
}

export class ReCacheIntDto extends PickType(SaveCacheIntDto, [
  'merchantId',
  'key',
  'options',
]) {}

class CacheParams {
  id?: number;
}

export class CacheOptions {
  query?: CacheQuery;
  params?: CacheParams;
  isDelete?: boolean;
}

export class CacheQuery {
  page?: number;
  limit?: number;
  categoryIds?: number[];

  constructor(
    page: number = null,
    limit: number = null,
    categoryIds: number[] = null,
  ) {
    this.page = page;
    this.limit = limit;
    this.categoryIds = categoryIds;
  }
}
