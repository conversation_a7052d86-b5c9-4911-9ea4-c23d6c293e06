import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { TypeOrmCustomModule } from 'utility/dist';
import { MerchantRepository } from '../auth/repositories/merchant.repository';
import { <PERSON>ache<PERSON>ontroller } from './controllers/cache.controller';
import { CacheService } from './services/cache.service';

@Module({
  imports: [HttpModule, TypeOrmCustomModule.forFeature([MerchantRepository])],
  controllers: [CacheController],
  providers: [CacheService],
  exports: [CacheService],
})
export class CacheModule {}
