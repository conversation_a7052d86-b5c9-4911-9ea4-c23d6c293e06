import { Injectable } from '@nestjs/common';
import { paginate, Pagination } from 'nestjs-typeorm-paginate';
import { Brackets, In } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { User } from '../../../auth/entities/user.entity';
import { MerchantRepository } from '../../../auth/repositories/merchant.repository';
import { TypeORMQueryResult } from '../../../common/dtos/sql-query-result.dto';
import { NotFoundExc } from '../../../common/exceptions/custom.exception';
import { GroupPolicyResDto } from '../../dtos/common/res/group-policies.res.dto';
import { PolicyResDto } from '../../dtos/common/res/policy.res.dto';
import {
  CreateGroupPolicyMerchantReqDto,
  DeleteManyGroupPoliciesMerchantReqDto,
  GetListGroupPoliciesMerchantReqDto,
  GetListPoliciesMerchantReqDto,
  UpdateGroupPolicyMerchantReqDto,
} from '../../dtos/merchant/req/casl.merchant.req.dto';
import { GroupToPolicy } from '../../entities/group-to-policy.entity';
import {
  GroupPolicyStatus,
  GroupPolicyType,
} from '../../enums/group-policy.enum';
import { PolicyType } from '../../enums/policy.enum';
import { GroupPolicyRepository } from '../../repositories/group-policy.repository';
import { GroupToPolicyRepository } from '../../repositories/group-to-policy.repository';
import { PolicyRepository } from '../../repositories/policy.repository';
import { CaslCommonService } from '../common/casl.common.service';

@Injectable()
export class CaslMerchantService {
  constructor(
    private groupPolicyRepo: GroupPolicyRepository,
    private policyRepo: PolicyRepository,
    private groupToPolicyRepo: GroupToPolicyRepository,
    private caslCommonService: CaslCommonService,
    private merchantRepo: MerchantRepository,
  ) {}

  async getAllPolicies(dto: GetListPoliciesMerchantReqDto) {
    const { limit, page } = dto;
    let { searchText } = dto;

    const qb = this.policyRepo
      .createQueryBuilder('p')
      .where('p.type = :type', { type: PolicyType.COMMON })
      .orderBy('p.id');

    if (searchText) {
      searchText = `%${searchText}%`;
      qb.andWhere('p.name ILIKE :searchText', { searchText });
    }

    const { items, meta } = await paginate(qb, { limit, page });
    const policies = items.map((item) =>
      PolicyResDto.forMerchant({ data: item }),
    );

    return new Pagination(policies, meta);
  }

  async getListGroupPolicies(
    dto: GetListGroupPoliciesMerchantReqDto,
    user: User,
  ) {
    const { status, limit, page, type } = dto;
    let { searchText } = dto;

    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );
    const queryBuilder = this.groupPolicyRepo
      .createQueryBuilder('groupPolicy')
      .andWhere(
        new Brackets((qb) => {
          qb.where(
            new Brackets((qb2) => {
              qb2.where('groupPolicy.ownerId  = :rootMerchantUserId', {
                rootMerchantUserId,
              });
              qb2.andWhere('groupPolicy.type = :merchantType', {
                merchantType: GroupPolicyType.MERCHANT,
              });
            }),
          );

          qb.orWhere('groupPolicy.type = :commonType', {
            commonType: GroupPolicyType.COMMON,
          });
        }),
      )
      .orderBy('groupPolicy.id');

    if (searchText) {
      searchText = `%${searchText}%`;
      queryBuilder.where('groupPolicy.name ILIKE :searchText', { searchText });
    }
    if (status)
      queryBuilder.andWhere('groupPolicy.status = :status', { status });

    if (type) queryBuilder.andWhere('groupPolicy.type = :type', { type });

    const { items, meta } = await paginate(queryBuilder, {
      limit,
      page,
    });

    const groupPolicies = await Promise.all(
      items.map(async (item) => {
        const groupPolicy = await this.groupPolicyRepo.findOne({
          where: { id: item.id },
          relations: { groupToPolicies: { policy: true } },
        });
        return GroupPolicyResDto.forMerchant({ data: groupPolicy });
      }),
    );

    return new Pagination(groupPolicies, meta);
  }

  async getDetail(id: number, user: User) {
    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );
    const groupPolicy = await this.groupPolicyRepo
      .createQueryBuilder('groupPolicy')
      .leftJoinAndSelect('groupPolicy.groupToPolicies', 'groupToPolicy')
      .leftJoinAndSelect('groupToPolicy.policy', 'policy')
      .loadRelationCountAndMap(
        'groupPolicy.totalMem',
        'groupPolicy.userToGroupPolicies',
      )
      .where('groupPolicy.id = :id', { id })
      .andWhere('groupPolicy.type <> :adminType', { adminType: 'ADMIN' })
      .andWhere('groupPolicy.ownerId = :rootMerchantUserId', {
        rootMerchantUserId,
      })
      .getOne();
    if (!groupPolicy) throw new NotFoundExc({ message: 'common' });

    return GroupPolicyResDto.forMerchant({ data: groupPolicy });
  }

  @Transactional()
  async createGroupPolicy(
    reqData: CreateGroupPolicyMerchantReqDto,
    user: User,
  ) {
    const { description, name, policyIds } = reqData;
    const groupPolicyKey = this.caslCommonService.transformNameToKey(name);
    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );

    await this.checkPolicies(policyIds);

    await this.caslCommonService.checkGroupPolicyKey(
      GroupPolicyType.MERCHANT,
      groupPolicyKey,
      rootMerchantUserId,
    );

    const groupPolicy = this.groupPolicyRepo.create({
      key: groupPolicyKey,
      name,
      description,
      status: GroupPolicyStatus.ACTIVE,
      type: GroupPolicyType.MERCHANT,
      ownerId: rootMerchantUserId,
    });
    await this.groupPolicyRepo.save(groupPolicy);

    await this.saveGroupToPolicies([], policyIds, groupPolicy.id);

    return this.getDetail(groupPolicy.id, user);
  }

  @Transactional()
  async updateGroupPolicy(
    reqData: UpdateGroupPolicyMerchantReqDto,
    user: User,
  ) {
    const { id, description, name, policyIds, status } = reqData;
    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );

    const groupPolicy = await this.groupPolicyRepo.findOneOrThrowNotFoundExc({
      where: {
        id,
        type: GroupPolicyType.MERCHANT,
        ownerId: rootMerchantUserId,
      },
      relations: { groupToPolicies: true },
    });
    await this.checkPolicies(policyIds);
    const updatedKey = this.caslCommonService.transformNameToKey(name);
    if (groupPolicy.key !== updatedKey)
      await this.caslCommonService.checkGroupPolicyKey(
        GroupPolicyType.MERCHANT,
        updatedKey,
        rootMerchantUserId,
      );
    await this.saveGroupToPolicies(
      groupPolicy.groupToPolicies,
      policyIds,
      groupPolicy.id,
    );
    await this.groupPolicyRepo.update(id, {
      key: updatedKey,
      name,
      description,
      status,
    });

    return this.getDetail(id, user);
  }

  private async saveGroupToPolicies(
    groupToPolicies: GroupToPolicy[],
    policyIds: number[],
    groupPolicyId: number,
  ) {
    const groupToPolicyIdsToRemove: number[] = [];
    const groupToPolicyToInsert: GroupToPolicy[] = [];

    for (const groupToPolicy of groupToPolicies) {
      if (!policyIds.includes(groupToPolicy.policyId)) {
        groupToPolicyIdsToRemove.push(groupToPolicy.id);
      }
    }

    for (const policyId of policyIds) {
      if (!groupToPolicies.some((item) => item.policyId === policyId)) {
        const groupToPolicy = this.groupToPolicyRepo.create({
          groupPolicyId,
          policyId,
        });
        groupToPolicyToInsert.push(groupToPolicy);
      }
    }

    await Promise.all([
      groupToPolicyIdsToRemove?.length &&
        this.groupToPolicyRepo.softDelete(groupToPolicyIdsToRemove),
      groupToPolicyToInsert?.length &&
        this.groupToPolicyRepo.insert(groupToPolicyToInsert),
    ]);
  }

  private async checkPolicies(policyIds: number[]) {
    await Promise.all(
      policyIds.map((item) =>
        this.policyRepo.findOneByOrThrowNotFoundExc({
          id: item,
          type: PolicyType.COMMON,
        }),
      ),
    );
  }

  async deleteListGroupPolicies(
    reqData: DeleteManyGroupPoliciesMerchantReqDto,
    user: User,
  ): Promise<TypeORMQueryResult> {
    const { groupPoliciesIds } = reqData;
    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );
    const groupPolicies = await this.groupPolicyRepo.findBy({
      id: In(groupPoliciesIds),
      type: GroupPolicyType.MERCHANT,
      ownerId: rootMerchantUserId,
    });

    if (!groupPolicies) throw new NotFoundExc({ message: 'common' });

    return this.groupPolicyRepo.softDelete(groupPoliciesIds);
  }

  async deleteGroupPolicyById(id: number, user: User) {
    const rootMerchantUserId = await this.merchantRepo.getRootMerchantUserId(
      user.merchant,
    );
    const groupPolicy = await this.groupPolicyRepo.findOne({
      where: {
        id,
        type: GroupPolicyType.MERCHANT,
        ownerId: rootMerchantUserId,
      },
    });
    if (!groupPolicy) throw new NotFoundExc({ message: 'common' });

    return await this.groupPolicyRepo.softDelete(id);
  }
}
