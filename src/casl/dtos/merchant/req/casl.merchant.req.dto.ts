import {
  IsValidArrayNumber,
  IsValidEnum,
  IsValidNumber,
  IsValidText,
} from '../../../../common/decorators/custom-validator.decorator';
import { PaginationReqDto } from '../../../../common/dtos/pagination.dto';
import {
  GroupPolicyStatus,
  GroupPolicyType,
} from '../../../enums/group-policy.enum';

export class GetListPoliciesMerchantReqDto extends PaginationReqDto {
  @IsValidText({ required: false })
  searchText?: string;
}

export class GetListGroupPoliciesMerchantReqDto extends PaginationReqDto {
  @IsValidText({ required: false })
  searchText?: string;

  @IsValidEnum({ enum: GroupPolicyStatus, required: false })
  status?: GroupPolicyStatus;

  @IsValidEnum({ enum: GroupPolicyType, required: false })
  type?: GroupPolicyType;
}

export class SaveGroupPolicyMerchantReqDto {
  @IsValidText()
  name: string;

  @IsValidText()
  description: string;

  @IsValidArrayNumber()
  policyIds: number[];
}

export class CreateGroupPolicyMerchantReqDto extends SaveGroupPolicyMerchantReqDto {}

export class UpdateGroupPolicyMerchantReqDto extends SaveGroupPolicyMerchantReqDto {
  @IsValidNumber()
  id: number;

  @IsValidEnum({ enum: GroupPolicyStatus })
  status: GroupPolicyStatus;
}

export class DeleteManyGroupPoliciesMerchantReqDto {
  @IsValidArrayNumber()
  groupPoliciesIds: number[];
}
