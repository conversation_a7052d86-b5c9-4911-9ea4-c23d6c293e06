import { BaseResponseDtoParams } from '../../../../common/dtos/base.res';
import {
  Action,
  ActionAbility,
  Resource,
} from '../../../../common/enums/casl.enum';
import { Policy } from '../../../entities/policy.entity';

export interface PolicyResDtoParams extends BaseResponseDtoParams {
  data: Policy;
}

export class PolicyResDto {
  id: number;
  name: string;
  action: Action;
  resource: Resource;
  actionAbility: ActionAbility;

  static mapProperty(dto: PolicyResDto, { data }: PolicyResDtoParams) {
    dto.id = data.id;
    dto.name = data.name;
    dto.action = data.action;
    dto.resource = data.resource;
    dto.actionAbility = data.actionAbility;
  }

  static forAdmin(params: PolicyResDtoParams) {
    const { data } = params;

    if (!data) return null;
    const result = new PolicyResDto();

    PolicyResDto.mapProperty(result, params);

    return result;
  }
  static forMerchant(params: PolicyResDtoParams) {
    const { data } = params;

    const result = new PolicyResDto();
    if (!data) return null;

    PolicyResDto.mapProperty(result, params);

    return result;
  }
}
