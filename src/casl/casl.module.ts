import { Global, Module } from '@nestjs/common';
import { TypeOrmCustomModule } from 'utility/dist';
import { MerchantRepository } from '../auth/repositories/merchant.repository';
import { UserRepository } from '../auth/repositories/user.repository';
import { CaslAbilityFactory } from './casl-ability.factory';
import { CaslAdminController } from './controllers/casl.admin.controller';
import { CaslMerchantController } from './controllers/casl.merchant.controller';
import { GroupPolicyRepository } from './repositories/group-policy.repository';
import { GroupToPolicyRepository } from './repositories/group-to-policy.repository';
import { PolicyRepository } from './repositories/policy.repository';
import { UserToGroupPolicyRepository } from './repositories/user-to-group-policy.repository';
import { CaslAdminService } from './services/admin/casl.admin.service';
import { CaslCommonService } from './services/common/casl.common.service';
import { CaslMerchantService } from './services/merchant/casl.merchant.service';
import { CaslTaskService } from './services/task/casl.task.service';
import { JwtCaslAdminStrategy } from './strategies/jwt-casl.admin.strategy';
import { JwtCaslSMerchantStrategy } from './strategies/jwt-casl.merchant.strategy';

@Global()
@Module({
  imports: [
    TypeOrmCustomModule.forFeature([
      UserRepository,
      GroupPolicyRepository,
      GroupToPolicyRepository,
      PolicyRepository,
      UserToGroupPolicyRepository,
      MerchantRepository,
      PolicyRepository,
    ]),
  ],
  controllers: [CaslMerchantController, CaslAdminController],
  providers: [
    CaslMerchantService,
    CaslAdminService,
    CaslAbilityFactory,
    JwtCaslAdminStrategy,
    JwtCaslSMerchantStrategy,
    CaslCommonService,
    CaslTaskService,
  ],
  exports: [CaslAbilityFactory],
})
export class CaslModule {}
