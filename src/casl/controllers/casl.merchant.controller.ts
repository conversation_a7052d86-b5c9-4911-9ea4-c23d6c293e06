import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { User } from '../../auth/entities/user.entity';
import { PrefixType } from '../../common/constants/global.constant';
import {
  AuthorizeMerchant,
  CurrentAuthData,
} from '../../common/decorators/auth.decorator';
import { PaginationResponse } from '../../common/decorators/swagger.decorator';
import { Action, Resource } from '../../common/enums/casl.enum';
import { GroupPolicyResDto } from '../dtos/common/res/group-policies.res.dto';
import { PolicyResDto } from '../dtos/common/res/policy.res.dto';
import {
  CreateGroupPolicyMerchantReqDto,
  DeleteManyGroupPoliciesMerchantReqDto,
  GetListGroupPoliciesMerchantReqDto,
  GetListPoliciesMerchantReqDto,
  UpdateGroupPolicyMerchantReqDto,
} from '../dtos/merchant/req/casl.merchant.req.dto';
import { CaslMerchantService } from '../services/merchant/casl.merchant.service';

@Controller(`${PrefixType.MERCHANT}/casl`)
@ApiTags('Casl Merchant')
@AuthorizeMerchant({ action: Action.READ, resource: Resource.GROUP_POLICY })
export class CaslMerchantController {
  constructor(private caslMerchantService: CaslMerchantService) {}

  @Get('policies')
  @PaginationResponse(PolicyResDto)
  getAllPolicies(@Query() query: GetListPoliciesMerchantReqDto) {
    return this.caslMerchantService.getAllPolicies(query);
  }

  @Get('group-policy')
  @PaginationResponse(GroupPolicyResDto)
  getAllGroupPolicies(
    @Query() query: GetListGroupPoliciesMerchantReqDto,
    @CurrentAuthData() user: User,
  ) {
    return this.caslMerchantService.getListGroupPolicies(query, user);
  }

  @Get('group-policy/:id')
  getGroupPolicyById(@Param('id') id: number, @CurrentAuthData() user: User) {
    return this.caslMerchantService.getDetail(id, user);
  }

  @Post('group-policy')
  @AuthorizeMerchant({ action: Action.CREATE, resource: Resource.GROUP_POLICY })
  createGroupPolicies(
    @Body() body: CreateGroupPolicyMerchantReqDto,
    @CurrentAuthData() user: User,
  ) {
    return this.caslMerchantService.createGroupPolicy(body, user);
  }

  @Put('group-policy')
  @AuthorizeMerchant({ action: Action.UPDATE, resource: Resource.GROUP_POLICY })
  updateGroupPolicies(
    @Body() body: UpdateGroupPolicyMerchantReqDto,
    @CurrentAuthData() user: User,
  ) {
    return this.caslMerchantService.updateGroupPolicy(body, user);
  }

  @Delete('group-policy')
  @AuthorizeMerchant({ action: Action.DELETE, resource: Resource.GROUP_POLICY })
  deleteListGroupPolicies(
    @Body() body: DeleteManyGroupPoliciesMerchantReqDto,
    @CurrentAuthData() user: User,
  ) {
    return this.caslMerchantService.deleteListGroupPolicies(body, user);
  }

  @Delete('group-policy/:id')
  @AuthorizeMerchant({ action: Action.DELETE, resource: Resource.GROUP_POLICY })
  deleteGroupPolicyById(
    @Param('id') id: number,
    @CurrentAuthData() user: User,
  ) {
    return this.caslMerchantService.deleteGroupPolicyById(id, user);
  }
}
