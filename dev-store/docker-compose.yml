version: '3.9'

services:
  mandala-postgres-dev:
    image: postgres:17.4
    container_name: mandala-postgres-dev
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: Eazmn3PPplDKX9K9
      POSTGRES_DB: mandala
    volumes:
      - mandala-postgres-dev-data:/var/lib/postgresql/data
    ports:
      - '5432:5432'
    restart: always
    networks:
      - mandala-dev-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

  mandala-redis-dev:
    image: redis:7.4.3
    container_name: mandala-redis-dev
    ports:
      - '6379:6379'
    command: ['redis-server', '--bind', '0.0.0.0', '--appendonly', 'yes']
    volumes:
      - mandala-redis-dev-data:/data
    restart: always
    networks:
      - mandala-dev-network
    deploy:
      resources:
        limits:
          cpus: '0.3'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 128M

volumes:
  mandala-postgres-dev-data:
    name: mandala-postgres-dev-data
  mandala-redis-dev-data:
    name: mandala-redis-dev-data

networks:
  mandala-dev-network:
    driver: bridge
    name: mandala-dev-network
