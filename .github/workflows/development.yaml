name: development

on:
  push:
    branches:
      - main

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    # Checkout the code
    - name: Checkout code
      uses: actions/checkout@v3

    # Set up Docker
    - name: Set up Docker
      uses: docker/setup-buildx-action@v2

    # Build Docker image
    - name: Build Docker Image
      run: |
        docker build -t mandala-be-dev .

    # Save Docker image as a tar file
    - name: Save Docker Image
      run: |
        docker save mandala-be-dev > mandala-be-dev.tar

    # Transfer Docker image to VPS
    - name: Transfer Docker Image to VPS
      uses: appleboy/scp-action@v0.1.7
      with:
        host: ${{ secrets.VPS_IP }}
        username: ${{ secrets.VPS_USER }}
        key: ${{ secrets.VPS_KEY }}
        source: mandala-be-dev.tar
        target: ~/mandala/registries

  deploy:
    runs-on: ubuntu-latest
    needs: build

    steps:
    # SSH into VPS and Deploy
    - name: SSH into VPS and Deploy
      uses: appleboy/ssh-action@v1.2.0
      with:
        host: ${{ secrets.VPS_IP }}
        username: ${{ secrets.VPS_USER }}
        key: ${{ secrets.VPS_KEY }}
        script: |
          # Load the Docker image
          docker load < ~/mandala/registries/mandala-be-dev.tar

          # Stop and remove any existing container
          docker stop mandala-be-dev || true
          docker stop mandala-be-worker-dev || true
          docker rm mandala-be-dev || true
          docker rm mandala-be-worker-dev || true

          # Create the named volume if it doesn’t exist
          docker volume create mandala-dev-data || true

          # Create the named network if it doesn’t exist
          docker network create mandala-dev-network || true

          # Run backend container
          docker run -p 5005:5000 \
            --env-file ~/mandala/env-configs/.dev.env \
            --name mandala-be-dev \
            --restart always \
            --network mandala-dev-network \
            -v mandala-dev-data:/var/lib/mandala \
            -d mandala-be-dev npm run start:prod

          # Run worker container
          docker run -p 5006:5001 \
            --env-file ~/mandala/env-configs/.dev.env \
            --name mandala-be-worker-dev \
            --restart always \
            --network mandala-dev-network \
            -v mandala-dev-data:/var/lib/mandala \
            -d mandala-be-dev npm run start:worker