name: staging

on:
  push:
    branches:
      - staging

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    # Checkout the code
    - name: Checkout code
      uses: actions/checkout@v3

    # Set up Docker
    - name: Set up Docker
      uses: docker/setup-buildx-action@v2

    # Build Docker image
    - name: Build Docker Image
      run: |
        docker build -t mandala-be-stg .

    # Save Docker image as a tar file
    - name: Save Docker Image
      run: |
        docker save mandala-be-stg > mandala-be-stg.tar

    # Transfer Docker image to VPS
    - name: Transfer Docker Image to VPS
      uses: appleboy/scp-action@v0.1.7
      with:
        host: ${{ secrets.VPS_IP }}
        username: ${{ secrets.VPS_USER }}
        key: ${{ secrets.VPS_KEY }}
        source: mandala-be-stg.tar
        target: ~/mandala/registries

  deploy:
    runs-on: ubuntu-latest
    needs: build

    steps:
    # SSH into VPS and Deploy
    - name: SSH into VPS and Deploy
      uses: appleboy/ssh-action@v1.2.0
      with:
        host: ${{ secrets.VPS_IP }}
        username: ${{ secrets.VPS_USER }}
        key: ${{ secrets.VPS_KEY }}
        script: |
          # Load the Docker image
          docker load < ~/mandala/registries/mandala-be-stg.tar

          # Stop and remove any existing container
          docker stop mandala-be-stg || true
          docker stop mandala-be-worker-stg || true
          docker rm mandala-be-stg || true
          docker rm mandala-be-worker-stg || true

          # Create the named volume if it doesn’t exist
          docker volume create mandala-stg-data || true

          # Create the named network if it doesn’t exist
          docker network create mandala-stg-network || true

          # Run backend container
          docker run -p 5002:5000 \
            --env-file ~/mandala/env-configs/.stg.env \
            --name mandala-be-stg \
            --restart always \
            --network mandala-stg-network \
            -v mandala-stg-data:/var/lib/mandala \
            -d mandala-be-stg npm run start:prod

          # Run worker container
          docker run -p 5003:5001 \
            --env-file ~/mandala/env-configs/.stg.env \
            --name mandala-be-worker-stg \
            --restart always \
            --network mandala-stg-network \
            -v mandala-stg-data:/var/lib/mandala \
            -d mandala-be-stg npm run start:worker